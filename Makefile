ifeq ($(OS),Windows_NT)
    EXE_EXT := .exe
else
    EXE_EXT :=
endif

NAME        = human_gl$(EXE_EXT)
NAME_DEBUG  = human_gl_debug$(EXE_EXT)

HEADER      = src/

SRC         = src/main.cpp \
              src/Application.cpp \
              src/MatrixStack.cpp \
              src/MenuSystem.cpp \
              src/AnimationSystem.cpp \
              src/BodyParts.cpp

CC          = g++

OPT_LEVEL ?= 0

ifeq ($(OPT_LEVEL),0)
	OPT_FLAGS = -O0 -g
else ifeq ($(OPT_LEVEL),1)
	OPT_FLAGS = -O1 -flto -s -ffunction-sections -fdata-sections -Wl,--gc-sections
else ifeq ($(OPT_LEVEL),2)
	OPT_FLAGS = -O2 -flto -s -ffunction-sections -fdata-sections -Wl,--gc-sections
else ifeq ($(OPT_LEVEL),3)
	OPT_FLAGS = -O3 -flto -s -ffunction-sections -fdata-sections -Wl,--gc-sections
else
	$(error Unsupported OPT_LEVEL=$(OPT_LEVEL))
endif

# SDL2 paths for macOS (Homebrew)
SDL2_PREFIX = $(shell brew --prefix sdl2 2>/dev/null || echo /usr/local)
INCLUDE_FLAGS = -I$(SDL2_PREFIX)/include

COMPILE_FLAGS = $(OPT_FLAGS) $(INCLUDE_FLAGS)
#-Wold-style-cast -Wshadow -Wconversion -Wformat=2 -Wundef \
				-Wfloat-equal -Wzero-as-null-pointer-constant 
#-Wall -Wextra -std=c++14 -Wmissing-declarations \

CFLAGS = $(COMPILE_FLAGS)

ifeq ($(OS),Windows_NT)
    MKDIR   = mkdir
    RMDIR   = rmdir /S /Q
    RM      = del /F /Q
else
    MKDIR   = mkdir -p
    RMDIR   = rm -rf
    RM      = rm -f
endif

OBJ_DIR         = ./objs
OBJ_DIR_DEBUG   = ./objs_debug

ENABLE_LTO  ?= 0
ENABLE_PGO  ?= 0
export ENABLE_LTO ENABLE_PGO

ifeq ($(ENABLE_LTO),1)
    COMPILE_FLAGS   += -flto
    LDFLAGS  += -flto
endif

ifeq ($(ENABLE_PGO),1)
    COMPILE_FLAGS  += -fprofile-generate
endif

ifeq ($(DEBUG),1)
    CFLAGS    += -DDEBUG=1
    OBJ_DIR    = $(OBJ_DIR_DEBUG)
    TARGET     = $(NAME_DEBUG)
else
    TARGET     = $(NAME)
endif

export COMPILE_FLAGS

# SDL2 and OpenGL libraries
ifeq ($(OS),Windows_NT)
    LDFLAGS     = -lmingw32 -lSDL2main -lSDL2 -lopengl32 -lglu32
else
    LDFLAGS     = -L$(SDL2_PREFIX)/lib -lSDL2 -framework OpenGL
endif

OBJS        = $(SRC:%.cpp=$(OBJ_DIR)/%.o)

all: dirs $(TARGET)

dirs:
	-$(MKDIR) $(OBJ_DIR)
	-$(MKDIR) $(OBJ_DIR_DEBUG)
	-$(MKDIR) $(OBJ_DIR)/src
	-$(MKDIR) $(OBJ_DIR_DEBUG)/src

debug:
	$(MAKE) all DEBUG=1

$(TARGET): $(OBJS)
	$(CC) $(CFLAGS) $(OBJS) -o $@ $(LDFLAGS)

$(OBJ_DIR)/%.o: %.cpp $(HEADER)
	$(CC) $(CFLAGS) -c $< -o $@

clean:
	-$(RM) $(OBJ_DIR)/*.o $(OBJ_DIR_DEBUG)/*.o

fclean: clean
	-$(RM) $(NAME) $(NAME_DEBUG)
	-$(RMDIR) $(OBJ_DIR) $(OBJ_DIR_DEBUG) data

re: fclean all

both: all debug

re_both: re both

.PHONY: all dirs clean fclean re debug both re_both
