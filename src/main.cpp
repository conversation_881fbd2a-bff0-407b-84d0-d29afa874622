#define GL_SILENCE_DEPRECATION
#include <SDL2/SDL.h>
#include <SDL2/SDL_opengl.h>
#include <OpenGL/gl.h>
#include <OpenGL/glu.h>
#include <iostream>
#include <cstdlib>
#include <cmath>

class Application {
private:
    SDL_Window* window;
    SDL_GLContext glContext;
    bool running;
    int windowWidth;
    int windowHeight;

    // Rotation angles for the cube
    float rotationX;
    float rotationY;

    // Keyboard state
    const Uint8* keyboardState;

    // Helper method to set up 3D perspective projection
    void setupPerspective() {
        glMatrixMode(GL_PROJECTION);
        glLoadIdentity();

        // Set up perspective projection
        // gluPerspective(fovy, aspect, near, far)
        GLdouble fovy = 45.0;  // Field of view in degrees
        GLdouble aspect = static_cast<GLdouble>(windowWidth) / static_cast<GLdouble>(windowHeight);
        GLdouble nearPlane = 0.1;
        GLdouble farPlane = 100.0;
        gluPerspective(fovy, aspect, nearPlane, farPlane);

        glMatrixMode(GL_MODELVIEW);
        glLoadIdentity();
    }

    // Handle continuous keyboard input for rotation
    void handleKeyboardInput() {
        const float rotationSpeed = 2.0f;  // degrees per frame

        if (keyboardState[SDL_SCANCODE_W]) {
            rotationX -= rotationSpeed;  // Rotate up
        }
        if (keyboardState[SDL_SCANCODE_S]) {
            rotationX += rotationSpeed;  // Rotate down
        }
        if (keyboardState[SDL_SCANCODE_A]) {
            rotationY -= rotationSpeed;  // Rotate left
        }
        if (keyboardState[SDL_SCANCODE_D]) {
            rotationY += rotationSpeed;  // Rotate right
        }

        // Keep rotation angles in reasonable range
        if (rotationX > 360.0f) rotationX -= 360.0f;
        if (rotationX < -360.0f) rotationX += 360.0f;
        if (rotationY > 360.0f) rotationY -= 360.0f;
        if (rotationY < -360.0f) rotationY += 360.0f;
    }

public:
    Application() : window(nullptr), glContext(nullptr), running(false),
                   windowWidth(800), windowHeight(600), rotationX(0.0f), rotationY(0.0f),
                   keyboardState(nullptr) {}

    ~Application() {
        cleanup();
    }

    bool initialize() {
        // Initialize SDL
        if (SDL_Init(SDL_INIT_VIDEO) < 0) {
            std::cerr << "Failed to initialize SDL: " << SDL_GetError() << std::endl;
            return false;
        }

        // Set OpenGL attributes (using compatibility profile for immediate mode)
        SDL_GL_SetAttribute(SDL_GL_CONTEXT_MAJOR_VERSION, 2);
        SDL_GL_SetAttribute(SDL_GL_CONTEXT_MINOR_VERSION, 1);
        SDL_GL_SetAttribute(SDL_GL_DOUBLEBUFFER, 1);
        SDL_GL_SetAttribute(SDL_GL_DEPTH_SIZE, 24);

        // Create window
        window = SDL_CreateWindow(
            "HumanGL - Rotating Cube (WASD to rotate)",
            SDL_WINDOWPOS_CENTERED,
            SDL_WINDOWPOS_CENTERED,
            windowWidth,
            windowHeight,
            SDL_WINDOW_OPENGL | SDL_WINDOW_SHOWN
        );

        if (!window) {
            std::cerr << "Failed to create window: " << SDL_GetError() << std::endl;
            return false;
        }

        // Create OpenGL context
        glContext = SDL_GL_CreateContext(window);
        if (!glContext) {
            std::cerr << "Failed to create OpenGL context: " << SDL_GetError() << std::endl;
            return false;
        }

        // Enable VSync
        SDL_GL_SetSwapInterval(1);

        // Initialize OpenGL settings
        glViewport(0, 0, windowWidth, windowHeight);
        glClearColor(0.2f, 0.3f, 0.4f, 1.0f);  // Nice blue-gray background
        glEnable(GL_DEPTH_TEST);

        std::cout << "OpenGL Version: " << glGetString(GL_VERSION) << std::endl;
        std::cout << "OpenGL Renderer: " << glGetString(GL_RENDERER) << std::endl;

        // Set up 3D perspective projection
        setupPerspective();

        // Get keyboard state
        keyboardState = SDL_GetKeyboardState(nullptr);

        running = true;
        return true;
    }

    void handleEvents() {
        SDL_Event event;
        while (SDL_PollEvent(&event)) {
            switch (event.type) {
                case SDL_QUIT:
                    running = false;
                    break;
                case SDL_KEYDOWN:
                    if (event.key.keysym.sym == SDLK_ESCAPE) {
                        running = false;
                    }
                    break;
                case SDL_WINDOWEVENT:
                    if (event.window.event == SDL_WINDOWEVENT_RESIZED) {
                        windowWidth = event.window.data1;
                        windowHeight = event.window.data2;
                        glViewport(0, 0, windowWidth, windowHeight);
                        setupPerspective();  // Update perspective on resize
                    }
                    break;
            }
        }
    }

    void render() {
        // Clear the screen
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

        // Set up the modelview matrix
        glLoadIdentity();

        // Move the cube back so we can see it
        glTranslatef(0.0f, 0.0f, -5.0f);

        // Apply rotations
        glRotatef(rotationX, 1.0f, 0.0f, 0.0f);  // Rotate around X-axis
        glRotatef(rotationY, 0.0f, 1.0f, 0.0f);  // Rotate around Y-axis

        // Draw a 3D cube using immediate mode OpenGL
        glBegin(GL_QUADS);

        // Front face (red)
        glColor3f(1.0f, 0.0f, 0.0f);
        glVertex3f(-1.0f, -1.0f,  1.0f);  // Bottom left
        glVertex3f( 1.0f, -1.0f,  1.0f);  // Bottom right
        glVertex3f( 1.0f,  1.0f,  1.0f);  // Top right
        glVertex3f(-1.0f,  1.0f,  1.0f);  // Top left

        // Back face (green)
        glColor3f(0.0f, 1.0f, 0.0f);
        glVertex3f(-1.0f, -1.0f, -1.0f);  // Bottom left
        glVertex3f(-1.0f,  1.0f, -1.0f);  // Top left
        glVertex3f( 1.0f,  1.0f, -1.0f);  // Top right
        glVertex3f( 1.0f, -1.0f, -1.0f);  // Bottom right

        // Top face (blue)
        glColor3f(0.0f, 0.0f, 1.0f);
        glVertex3f(-1.0f,  1.0f, -1.0f);  // Back left
        glVertex3f(-1.0f,  1.0f,  1.0f);  // Front left
        glVertex3f( 1.0f,  1.0f,  1.0f);  // Front right
        glVertex3f( 1.0f,  1.0f, -1.0f);  // Back right

        // Bottom face (yellow)
        glColor3f(1.0f, 1.0f, 0.0f);
        glVertex3f(-1.0f, -1.0f, -1.0f);  // Back left
        glVertex3f( 1.0f, -1.0f, -1.0f);  // Back right
        glVertex3f( 1.0f, -1.0f,  1.0f);  // Front right
        glVertex3f(-1.0f, -1.0f,  1.0f);  // Front left

        // Right face (magenta)
        glColor3f(1.0f, 0.0f, 1.0f);
        glVertex3f( 1.0f, -1.0f, -1.0f);  // Back bottom
        glVertex3f( 1.0f,  1.0f, -1.0f);  // Back top
        glVertex3f( 1.0f,  1.0f,  1.0f);  // Front top
        glVertex3f( 1.0f, -1.0f,  1.0f);  // Front bottom

        // Left face (cyan)
        glColor3f(0.0f, 1.0f, 1.0f);
        glVertex3f(-1.0f, -1.0f, -1.0f);  // Back bottom
        glVertex3f(-1.0f, -1.0f,  1.0f);  // Front bottom
        glVertex3f(-1.0f,  1.0f,  1.0f);  // Front top
        glVertex3f(-1.0f,  1.0f, -1.0f);  // Back top

        glEnd();

        // Swap buffers
        SDL_GL_SwapWindow(window);
    }

    void run() {
        if (!initialize()) {
            return;
        }

        std::cout << "Window created successfully! Use WASD to rotate the cube, ESC to exit." << std::endl;

        while (running) {
            handleEvents();
            handleKeyboardInput();  // Handle continuous keyboard input
            render();

            // Small delay to prevent excessive CPU usage
            SDL_Delay(16); // ~60 FPS
        }
    }

    void cleanup() {
        if (glContext) {
            SDL_GL_DeleteContext(glContext);
            glContext = nullptr;
        }
        if (window) {
            SDL_DestroyWindow(window);
            window = nullptr;
        }
        SDL_Quit();
    }
};

int main(int argc, char* argv[]) {
    (void)argc;  // Suppress unused parameter warning
    (void)argv;  // Suppress unused parameter warning
    
    Application app;
    app.run();
    
    return 0;
}
