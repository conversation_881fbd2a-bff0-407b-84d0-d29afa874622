#define GL_SILENCE_DEPRECATION
#include <SDL2/SDL.h>
#include <SDL2/SDL_opengl.h>
#include <OpenGL/gl.h>
#include <OpenGL/glu.h>
#include <iostream>
#include <cstdlib>
#include <cmath>
#include <vector>
#include <stack>

// Simple 4x4 Matrix class for transformations
class Matrix4 {
public:
    float m[16];  // Column-major order (OpenGL style)

    Matrix4() {
        loadIdentity();
    }

    void loadIdentity() {
        for (int i = 0; i < 16; i++) {
            m[i] = 0.0f;
        }
        m[0] = m[5] = m[10] = m[15] = 1.0f;  // Diagonal elements
    }

    void translate(float x, float y, float z) {
        Matrix4 trans;
        trans.m[12] = x;
        trans.m[13] = y;
        trans.m[14] = z;
        *this = *this * trans;
    }

    void rotateX(float degrees) {
        float rad = degrees * M_PI / 180.0f;
        float c = cosf(rad);
        float s = sinf(rad);

        Matrix4 rot;
        rot.m[5] = c;   rot.m[6] = s;
        rot.m[9] = -s;  rot.m[10] = c;
        *this = *this * rot;
    }

    void rotateY(float degrees) {
        float rad = degrees * M_PI / 180.0f;
        float c = cosf(rad);
        float s = sinf(rad);

        Matrix4 rot;
        rot.m[0] = c;   rot.m[2] = -s;
        rot.m[8] = s;   rot.m[10] = c;
        *this = *this * rot;
    }

    void rotateZ(float degrees) {
        float rad = degrees * M_PI / 180.0f;
        float c = cosf(rad);
        float s = sinf(rad);

        Matrix4 rot;
        rot.m[0] = c;   rot.m[1] = s;
        rot.m[4] = -s;  rot.m[5] = c;
        *this = *this * rot;
    }

    void scale(float x, float y, float z) {
        Matrix4 scl;
        scl.m[0] = x;
        scl.m[5] = y;
        scl.m[10] = z;
        *this = *this * scl;
    }

    Matrix4 operator*(const Matrix4& other) const {
        Matrix4 result;
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                result.m[i * 4 + j] = 0.0f;
                for (int k = 0; k < 4; k++) {
                    result.m[i * 4 + j] += m[k * 4 + j] * other.m[i * 4 + k];
                }
            }
        }
        return result;
    }

    void applyToOpenGL() const {
        glMultMatrixf(m);
    }
};

// Matrix Stack for hierarchical transformations
class MatrixStack {
private:
    std::stack<Matrix4> stack;
    Matrix4 current;

public:
    MatrixStack() {
        current.loadIdentity();
    }

    void pushMatrix() {
        stack.push(current);
    }

    void popMatrix() {
        if (!stack.empty()) {
            current = stack.top();
            stack.pop();
        }
    }

    void loadIdentity() {
        current.loadIdentity();
    }

    void translate(float x, float y, float z) {
        current.translate(x, y, z);
    }

    void rotateX(float degrees) {
        current.rotateX(degrees);
    }

    void rotateY(float degrees) {
        current.rotateY(degrees);
    }

    void rotateZ(float degrees) {
        current.rotateZ(degrees);
    }

    void scale(float x, float y, float z) {
        current.scale(x, y, z);
    }

    void applyToOpenGL() const {
        current.applyToOpenGL();
    }
};

class Application {
private:
    SDL_Window* window;
    SDL_GLContext glContext;
    bool running;
    int windowWidth;
    int windowHeight;

    // Application states
    enum AppState {
        MAIN_MENU,
        SETTINGS_MENU,
        CREDITS_MENU,
        INSTRUCTIONS_MENU,
        SIMULATION
    };

    AppState currentState;

    // Menu system
    int mouseX, mouseY;
    bool mousePressed;

    // Menu colors
    float menuBackgroundR, menuBackgroundG, menuBackgroundB;

    // Body part customization
    struct BodyPartSettings {
        float scaleX, scaleY, scaleZ;
        float colorR, colorG, colorB;

        BodyPartSettings() : scaleX(1.0f), scaleY(1.0f), scaleZ(1.0f),
                           colorR(0.8f), colorG(0.6f), colorB(0.4f) {}
    };

    BodyPartSettings headSettings;
    BodyPartSettings torsoSettings;
    BodyPartSettings leftArmSettings;
    BodyPartSettings rightArmSettings;
    BodyPartSettings leftLegSettings;
    BodyPartSettings rightLegSettings;

    // Settings menu state
    enum SettingsPage {
        SETTINGS_MAIN,
        BODY_CUSTOMIZATION,
        RESOLUTION_SETTINGS
    };

    SettingsPage currentSettingsPage;

    // UI Components
    struct Slider {
        int x, y, width, height;
        float minValue, maxValue, currentValue;
        std::string label;
        bool dragging;

        Slider(int x, int y, int w, int h, float min, float max, float current, const std::string& lbl)
            : x(x), y(y), width(w), height(h), minValue(min), maxValue(max),
              currentValue(current), label(lbl), dragging(false) {}

        bool isClicked(int mouseX, int mouseY) const {
            return mouseX >= x && mouseX <= x + width &&
                   mouseY >= y && mouseY <= y + height;
        }

        void updateValue(int mouseX) {
            if (dragging) {
                float ratio = static_cast<float>(mouseX - x) / static_cast<float>(width);
                ratio = std::max(0.0f, std::min(1.0f, ratio));
                currentValue = minValue + ratio * (maxValue - minValue);
            }
        }
    };

    struct ColorPicker {
        int x, y, size;
        float* colorR;
        float* colorG;
        float* colorB;
        std::string label;

        ColorPicker(int x, int y, int s, float* r, float* g, float* b, const std::string& lbl)
            : x(x), y(y), size(s), colorR(r), colorG(g), colorB(b), label(lbl) {}

        bool isClicked(int mouseX, int mouseY) const {
            return mouseX >= x && mouseX <= x + size &&
                   mouseY >= y && mouseY <= y + size;
        }
    };

    // Menu button structure
    struct MenuButton {
        int x, y, width, height;
        std::string text;
        bool hovered;

        MenuButton(int x, int y, int w, int h, const std::string& t)
            : x(x), y(y), width(w), height(h), text(t), hovered(false) {}

        bool isClicked(int mouseX, int mouseY) const {
            return mouseX >= x && mouseX <= x + width &&
                   mouseY >= y && mouseY <= y + height;
        }
    };

    // Matrix stack for hierarchical transformations
    MatrixStack matrixStack;

    // Body part rotations
    float torsoRotationY;
    float headRotationX;
    float headRotationY;

    // Arm rotations (left and right)
    float leftUpperArmX, leftUpperArmZ;
    float leftForearmX;
    float rightUpperArmX, rightUpperArmZ;
    float rightForearmX;

    // Leg rotations (left and right)
    float leftThighX;
    float leftLowerLegX;
    float rightThighX;
    float rightLowerLegX;

    // Animation modes
    enum AnimationMode {
        NONE,
        WALKING,
        JUMPING,
        DANCING,
        KUNGFU
    };

    AnimationMode currentAnimation;
    float animationTime;
    float jumpHeight;

    // Keyboard state
    const Uint8* keyboardState;

    // Helper method to set up 3D perspective projection
    void setupPerspective() {
        glMatrixMode(GL_PROJECTION);
        glLoadIdentity();

        // Set up perspective projection
        // gluPerspective(fovy, aspect, near, far)
        GLdouble fovy = 45.0;  // Field of view in degrees
        GLdouble aspect = static_cast<GLdouble>(windowWidth) / static_cast<GLdouble>(windowHeight);
        GLdouble nearPlane = 0.1;
        GLdouble farPlane = 100.0;
        gluPerspective(fovy, aspect, nearPlane, farPlane);

        glMatrixMode(GL_MODELVIEW);
        glLoadIdentity();
    }

    // Draw a 1x1x1 unit cube with specified color
    void drawColoredCube(float r, float g, float b) {
        glBegin(GL_QUADS);
        glColor3f(r, g, b);  // Single color for all faces

        // Front face
        glVertex3f(-0.5f, -0.5f,  0.5f);  // Bottom left
        glVertex3f( 0.5f, -0.5f,  0.5f);  // Bottom right
        glVertex3f( 0.5f,  0.5f,  0.5f);  // Top right
        glVertex3f(-0.5f,  0.5f,  0.5f);  // Top left

        // Back face
        glVertex3f(-0.5f, -0.5f, -0.5f);  // Bottom left
        glVertex3f(-0.5f,  0.5f, -0.5f);  // Top left
        glVertex3f( 0.5f,  0.5f, -0.5f);  // Top right
        glVertex3f( 0.5f, -0.5f, -0.5f);  // Bottom right

        // Top face
        glVertex3f(-0.5f,  0.5f, -0.5f);  // Back left
        glVertex3f(-0.5f,  0.5f,  0.5f);  // Front left
        glVertex3f( 0.5f,  0.5f,  0.5f);  // Front right
        glVertex3f( 0.5f,  0.5f, -0.5f);  // Back right

        // Bottom face
        glVertex3f(-0.5f, -0.5f, -0.5f);  // Back left
        glVertex3f( 0.5f, -0.5f, -0.5f);  // Back right
        glVertex3f( 0.5f, -0.5f,  0.5f);  // Front right
        glVertex3f(-0.5f, -0.5f,  0.5f);  // Front left

        // Right face
        glVertex3f( 0.5f, -0.5f, -0.5f);  // Back bottom
        glVertex3f( 0.5f,  0.5f, -0.5f);  // Back top
        glVertex3f( 0.5f,  0.5f,  0.5f);  // Front top
        glVertex3f( 0.5f, -0.5f,  0.5f);  // Front bottom

        // Left face
        glVertex3f(-0.5f, -0.5f, -0.5f);  // Back bottom
        glVertex3f(-0.5f, -0.5f,  0.5f);  // Front bottom
        glVertex3f(-0.5f,  0.5f,  0.5f);  // Front top
        glVertex3f(-0.5f,  0.5f, -0.5f);  // Back top

        glEnd();
    }

    // Legacy method for compatibility
    void drawUnitCube() {
        drawColoredCube(0.8f, 0.6f, 0.4f);  // Default skin color
    }

    // Draw the torso (scaled unit cube) - shirt color
    void drawTorso() {
        matrixStack.pushMatrix();

        // Scale to make it look like a torso (wider and taller)
        matrixStack.scale(1.2f, 1.8f, 0.8f);

        // Apply transformations and draw
        glLoadIdentity();
        glTranslatef(0.0f, 0.0f, -8.0f);  // Move back to see the model
        matrixStack.applyToOpenGL();

        drawColoredCube(0.2f, 0.4f, 0.8f);  // Blue shirt

        matrixStack.popMatrix();
    }

    // Draw left shoulder joint
    void drawLeftShoulder() {
        matrixStack.pushMatrix();

        // Position at left shoulder
        matrixStack.translate(-0.8f, 0.7f, 0.0f);

        // Scale to make a small shoulder joint
        matrixStack.scale(0.4f, 0.4f, 0.4f);

        // Apply transformations and draw
        glLoadIdentity();
        glTranslatef(0.0f, 0.0f, -8.0f);
        matrixStack.applyToOpenGL();

        drawColoredCube(0.8f, 0.6f, 0.4f);  // Skin color

        matrixStack.popMatrix();
    }

    // Draw right shoulder joint
    void drawRightShoulder() {
        matrixStack.pushMatrix();

        // Position at right shoulder
        matrixStack.translate(0.8f, 0.7f, 0.0f);

        // Scale to make a small shoulder joint
        matrixStack.scale(0.4f, 0.4f, 0.4f);

        // Apply transformations and draw
        glLoadIdentity();
        glTranslatef(0.0f, 0.0f, -8.0f);
        matrixStack.applyToOpenGL();

        drawColoredCube(0.8f, 0.6f, 0.4f);  // Skin color

        matrixStack.popMatrix();
    }

    // Draw a more pronounced neck
    void drawNeck() {
        matrixStack.pushMatrix();

        // Position neck at top of torso
        matrixStack.translate(0.0f, 1.1f, 0.0f);

        // Apply head rotations to neck too (neck follows head movement)
        matrixStack.rotateX(headRotationX * 0.3f);  // Neck moves less than head
        matrixStack.rotateY(headRotationY * 0.3f);

        // Scale to make a more visible neck (taller and slightly wider)
        matrixStack.scale(0.5f, 0.6f, 0.5f);

        // Apply transformations and draw
        glLoadIdentity();
        glTranslatef(0.0f, 0.0f, -8.0f);
        matrixStack.applyToOpenGL();

        drawColoredCube(0.8f, 0.6f, 0.4f);  // Skin color

        matrixStack.popMatrix();
    }

    // Draw the head (smaller unit cube, positioned relative to torso)
    void drawHead() {
        matrixStack.pushMatrix();

        // Position head above neck
        matrixStack.translate(0.0f, 1.5f, 0.0f);  // Position above neck

        // Apply head rotations
        matrixStack.rotateX(headRotationX);
        matrixStack.rotateY(headRotationY);

        // Scale to make it look like a head (smaller but proportional)
        matrixStack.scale(0.9f, 0.9f, 0.9f);

        // Apply transformations and draw
        glLoadIdentity();
        glTranslatef(0.0f, 0.0f, -8.0f);  // Move back to see the model
        matrixStack.applyToOpenGL();

        drawColoredCube(0.8f, 0.6f, 0.4f);  // Skin color for head

        matrixStack.popMatrix();
    }

    // Simple bitmap font rendering using basic character patterns
    void renderChar(char c, int x, int y, float r, float g, float b) {
        glColor3f(r, g, b);

        // Simple 5x7 bitmap font patterns for basic characters
        bool pattern[7][5] = {false};

        switch (c) {
            case 'A': case 'a':
                pattern[0][1] = pattern[0][2] = pattern[0][3] = true;
                pattern[1][0] = pattern[1][4] = true;
                pattern[2][0] = pattern[2][4] = true;
                pattern[3][0] = pattern[3][1] = pattern[3][2] = pattern[3][3] = pattern[3][4] = true;
                pattern[4][0] = pattern[4][4] = true;
                pattern[5][0] = pattern[5][4] = true;
                pattern[6][0] = pattern[6][4] = true;
                break;
            case 'B': case 'b':
                pattern[0][0] = pattern[0][1] = pattern[0][2] = pattern[0][3] = true;
                pattern[1][0] = pattern[1][4] = true;
                pattern[2][0] = pattern[2][4] = true;
                pattern[3][0] = pattern[3][1] = pattern[3][2] = pattern[3][3] = true;
                pattern[4][0] = pattern[4][4] = true;
                pattern[5][0] = pattern[5][4] = true;
                pattern[6][0] = pattern[6][1] = pattern[6][2] = pattern[6][3] = true;
                break;
            case 'C': case 'c':
                pattern[0][1] = pattern[0][2] = pattern[0][3] = true;
                pattern[1][0] = pattern[1][4] = true;
                pattern[2][0] = true;
                pattern[3][0] = true;
                pattern[4][0] = true;
                pattern[5][0] = pattern[5][4] = true;
                pattern[6][1] = pattern[6][2] = pattern[6][3] = true;
                break;
            case 'D': case 'd':
                pattern[0][0] = pattern[0][1] = pattern[0][2] = pattern[0][3] = true;
                pattern[1][0] = pattern[1][4] = true;
                pattern[2][0] = pattern[2][4] = true;
                pattern[3][0] = pattern[3][4] = true;
                pattern[4][0] = pattern[4][4] = true;
                pattern[5][0] = pattern[5][4] = true;
                pattern[6][0] = pattern[6][1] = pattern[6][2] = pattern[6][3] = true;
                break;
            case 'E': case 'e':
                pattern[0][0] = pattern[0][1] = pattern[0][2] = pattern[0][3] = pattern[0][4] = true;
                pattern[1][0] = true;
                pattern[2][0] = true;
                pattern[3][0] = pattern[3][1] = pattern[3][2] = pattern[3][3] = true;
                pattern[4][0] = true;
                pattern[5][0] = true;
                pattern[6][0] = pattern[6][1] = pattern[6][2] = pattern[6][3] = pattern[6][4] = true;
                break;
            case 'F': case 'f':
                pattern[0][0] = pattern[0][1] = pattern[0][2] = pattern[0][3] = pattern[0][4] = true;
                pattern[1][0] = true;
                pattern[2][0] = true;
                pattern[3][0] = pattern[3][1] = pattern[3][2] = pattern[3][3] = true;
                pattern[4][0] = true;
                pattern[5][0] = true;
                pattern[6][0] = true;
                break;
            case 'G': case 'g':
                pattern[0][1] = pattern[0][2] = pattern[0][3] = true;
                pattern[1][0] = pattern[1][4] = true;
                pattern[2][0] = true;
                pattern[3][0] = pattern[3][2] = pattern[3][3] = pattern[3][4] = true;
                pattern[4][0] = pattern[4][4] = true;
                pattern[5][0] = pattern[5][4] = true;
                pattern[6][1] = pattern[6][2] = pattern[6][3] = true;
                break;
            case 'H': case 'h':
                pattern[0][0] = pattern[0][4] = true;
                pattern[1][0] = pattern[1][4] = true;
                pattern[2][0] = pattern[2][4] = true;
                pattern[3][0] = pattern[3][1] = pattern[3][2] = pattern[3][3] = pattern[3][4] = true;
                pattern[4][0] = pattern[4][4] = true;
                pattern[5][0] = pattern[5][4] = true;
                pattern[6][0] = pattern[6][4] = true;
                break;
            case 'I': case 'i':
                pattern[0][1] = pattern[0][2] = pattern[0][3] = true;
                pattern[1][2] = true;
                pattern[2][2] = true;
                pattern[3][2] = true;
                pattern[4][2] = true;
                pattern[5][2] = true;
                pattern[6][1] = pattern[6][2] = pattern[6][3] = true;
                break;
            case 'J': case 'j':
                pattern[0][0] = pattern[0][1] = pattern[0][2] = pattern[0][3] = pattern[0][4] = true;
                pattern[1][2] = true;
                pattern[2][2] = true;
                pattern[3][2] = true;
                pattern[4][0] = pattern[4][2] = true;
                pattern[5][0] = pattern[5][2] = true;
                pattern[6][1] = true;
                break;
            case 'K': case 'k':
                pattern[0][0] = pattern[0][4] = true;
                pattern[1][0] = pattern[1][3] = true;
                pattern[2][0] = pattern[2][2] = true;
                pattern[3][0] = pattern[3][1] = true;
                pattern[4][0] = pattern[4][2] = true;
                pattern[5][0] = pattern[5][3] = true;
                pattern[6][0] = pattern[6][4] = true;
                break;
            case 'L': case 'l':
                pattern[0][0] = true;
                pattern[1][0] = true;
                pattern[2][0] = true;
                pattern[3][0] = true;
                pattern[4][0] = true;
                pattern[5][0] = true;
                pattern[6][0] = pattern[6][1] = pattern[6][2] = pattern[6][3] = pattern[6][4] = true;
                break;
            case 'M': case 'm':
                pattern[0][0] = pattern[0][4] = true;
                pattern[1][0] = pattern[1][1] = pattern[1][3] = pattern[1][4] = true;
                pattern[2][0] = pattern[2][2] = pattern[2][4] = true;
                pattern[3][0] = pattern[3][4] = true;
                pattern[4][0] = pattern[4][4] = true;
                pattern[5][0] = pattern[5][4] = true;
                pattern[6][0] = pattern[6][4] = true;
                break;
            case 'N': case 'n':
                pattern[0][0] = pattern[0][4] = true;
                pattern[1][0] = pattern[1][1] = pattern[1][4] = true;
                pattern[2][0] = pattern[2][2] = pattern[2][4] = true;
                pattern[3][0] = pattern[3][2] = pattern[3][4] = true;
                pattern[4][0] = pattern[4][3] = pattern[4][4] = true;
                pattern[5][0] = pattern[5][4] = true;
                pattern[6][0] = pattern[6][4] = true;
                break;
            case 'O': case 'o':
                pattern[0][1] = pattern[0][2] = pattern[0][3] = true;
                pattern[1][0] = pattern[1][4] = true;
                pattern[2][0] = pattern[2][4] = true;
                pattern[3][0] = pattern[3][4] = true;
                pattern[4][0] = pattern[4][4] = true;
                pattern[5][0] = pattern[5][4] = true;
                pattern[6][1] = pattern[6][2] = pattern[6][3] = true;
                break;
            case 'P': case 'p':
                pattern[0][0] = pattern[0][1] = pattern[0][2] = pattern[0][3] = true;
                pattern[1][0] = pattern[1][4] = true;
                pattern[2][0] = pattern[2][4] = true;
                pattern[3][0] = pattern[3][1] = pattern[3][2] = pattern[3][3] = true;
                pattern[4][0] = true;
                pattern[5][0] = true;
                pattern[6][0] = true;
                break;
            case 'Q': case 'q':
                pattern[0][1] = pattern[0][2] = pattern[0][3] = true;
                pattern[1][0] = pattern[1][4] = true;
                pattern[2][0] = pattern[2][4] = true;
                pattern[3][0] = pattern[3][4] = true;
                pattern[4][0] = pattern[4][3] = pattern[4][4] = true;
                pattern[5][0] = pattern[5][4] = true;
                pattern[6][1] = pattern[6][2] = pattern[6][3] = pattern[6][4] = true;
                break;
            case 'R': case 'r':
                pattern[0][0] = pattern[0][1] = pattern[0][2] = pattern[0][3] = true;
                pattern[1][0] = pattern[1][4] = true;
                pattern[2][0] = pattern[2][4] = true;
                pattern[3][0] = pattern[3][1] = pattern[3][2] = pattern[3][3] = true;
                pattern[4][0] = pattern[4][2] = true;
                pattern[5][0] = pattern[5][3] = true;
                pattern[6][0] = pattern[6][4] = true;
                break;
            case 'S': case 's':
                pattern[0][1] = pattern[0][2] = pattern[0][3] = pattern[0][4] = true;
                pattern[1][0] = true;
                pattern[2][0] = true;
                pattern[3][1] = pattern[3][2] = pattern[3][3] = true;
                pattern[4][4] = true;
                pattern[5][4] = true;
                pattern[6][0] = pattern[6][1] = pattern[6][2] = pattern[6][3] = true;
                break;
            case 'T': case 't':
                pattern[0][0] = pattern[0][1] = pattern[0][2] = pattern[0][3] = pattern[0][4] = true;
                pattern[1][2] = true;
                pattern[2][2] = true;
                pattern[3][2] = true;
                pattern[4][2] = true;
                pattern[5][2] = true;
                pattern[6][2] = true;
                break;
            case 'U': case 'u':
                pattern[0][0] = pattern[0][4] = true;
                pattern[1][0] = pattern[1][4] = true;
                pattern[2][0] = pattern[2][4] = true;
                pattern[3][0] = pattern[3][4] = true;
                pattern[4][0] = pattern[4][4] = true;
                pattern[5][0] = pattern[5][4] = true;
                pattern[6][1] = pattern[6][2] = pattern[6][3] = true;
                break;
            case 'V': case 'v':
                pattern[0][0] = pattern[0][4] = true;
                pattern[1][0] = pattern[1][4] = true;
                pattern[2][0] = pattern[2][4] = true;
                pattern[3][0] = pattern[3][4] = true;
                pattern[4][1] = pattern[4][3] = true;
                pattern[5][1] = pattern[5][3] = true;
                pattern[6][2] = true;
                break;
            case 'W': case 'w':
                pattern[0][0] = pattern[0][4] = true;
                pattern[1][0] = pattern[1][4] = true;
                pattern[2][0] = pattern[2][4] = true;
                pattern[3][0] = pattern[3][4] = true;
                pattern[4][0] = pattern[4][2] = pattern[4][4] = true;
                pattern[5][0] = pattern[5][1] = pattern[5][3] = pattern[5][4] = true;
                pattern[6][0] = pattern[6][4] = true;
                break;
            case 'X': case 'x':
                pattern[0][0] = pattern[0][4] = true;
                pattern[1][1] = pattern[1][3] = true;
                pattern[2][2] = true;
                pattern[3][2] = true;
                pattern[4][2] = true;
                pattern[5][1] = pattern[5][3] = true;
                pattern[6][0] = pattern[6][4] = true;
                break;
            case 'Y': case 'y':
                pattern[0][0] = pattern[0][4] = true;
                pattern[1][0] = pattern[1][4] = true;
                pattern[2][1] = pattern[2][3] = true;
                pattern[3][2] = true;
                pattern[4][2] = true;
                pattern[5][2] = true;
                pattern[6][2] = true;
                break;
            case 'Z': case 'z':
                pattern[0][0] = pattern[0][1] = pattern[0][2] = pattern[0][3] = pattern[0][4] = true;
                pattern[1][4] = true;
                pattern[2][3] = true;
                pattern[3][2] = true;
                pattern[4][1] = true;
                pattern[5][0] = true;
                pattern[6][0] = pattern[6][1] = pattern[6][2] = pattern[6][3] = pattern[6][4] = true;
                break;
            case ' ':
                // Space - no pattern
                break;
            case '!':
                pattern[0][2] = true;
                pattern[1][2] = true;
                pattern[2][2] = true;
                pattern[3][2] = true;
                pattern[4][2] = true;
                pattern[6][2] = true;
                break;
            case '"':
                pattern[0][1] = pattern[0][3] = true;
                pattern[1][1] = pattern[1][3] = true;
                break;
            case '.':
                pattern[6][2] = true;
                break;
            case ',':
                pattern[5][2] = true;
                pattern[6][1] = true;
                break;
            case '?':
                pattern[0][1] = pattern[0][2] = pattern[0][3] = true;
                pattern[1][0] = pattern[1][4] = true;
                pattern[2][4] = true;
                pattern[3][3] = true;
                pattern[4][2] = true;
                pattern[6][2] = true;
                break;
            case '/':
                pattern[0][4] = true;
                pattern[1][3] = true;
                pattern[2][3] = true;
                pattern[3][2] = true;
                pattern[4][1] = true;
                pattern[5][1] = true;
                pattern[6][0] = true;
                break;
            case '\\':
                pattern[0][0] = true;
                pattern[1][1] = true;
                pattern[2][1] = true;
                pattern[3][2] = true;
                pattern[4][3] = true;
                pattern[5][3] = true;
                pattern[6][4] = true;
                break;
            case '-':
                pattern[3][1] = pattern[3][2] = pattern[3][3] = true;
                break;
            case '+':
                pattern[2][2] = true;
                pattern[3][1] = pattern[3][2] = pattern[3][3] = true;
                pattern[4][2] = true;
                break;
            case '=':
                pattern[2][1] = pattern[2][2] = pattern[2][3] = true;
                pattern[4][1] = pattern[4][2] = pattern[4][3] = true;
                break;
            case ':':
                pattern[2][2] = true;
                pattern[4][2] = true;
                break;
            case ';':
                pattern[2][2] = true;
                pattern[4][2] = true;
                pattern[5][1] = true;
                break;
            case '(':
                pattern[1][3] = true;
                pattern[2][2] = true;
                pattern[3][2] = true;
                pattern[4][2] = true;
                pattern[5][3] = true;
                break;
            case ')':
                pattern[1][1] = true;
                pattern[2][2] = true;
                pattern[3][2] = true;
                pattern[4][2] = true;
                pattern[5][1] = true;
                break;
            case '[':
                pattern[0][1] = pattern[0][2] = true;
                pattern[1][1] = true;
                pattern[2][1] = true;
                pattern[3][1] = true;
                pattern[4][1] = true;
                pattern[5][1] = true;
                pattern[6][1] = pattern[6][2] = true;
                break;
            case ']':
                pattern[0][2] = pattern[0][3] = true;
                pattern[1][3] = true;
                pattern[2][3] = true;
                pattern[3][3] = true;
                pattern[4][3] = true;
                pattern[5][3] = true;
                pattern[6][2] = pattern[6][3] = true;
                break;
            case '0':
                pattern[0][1] = pattern[0][2] = pattern[0][3] = true;
                pattern[1][0] = pattern[1][4] = true;
                pattern[2][0] = pattern[2][4] = true;
                pattern[3][0] = pattern[3][4] = true;
                pattern[4][0] = pattern[4][4] = true;
                pattern[5][0] = pattern[5][4] = true;
                pattern[6][1] = pattern[6][2] = pattern[6][3] = true;
                break;
            case '1':
                pattern[0][2] = true;
                pattern[1][1] = pattern[1][2] = true;
                pattern[2][2] = true;
                pattern[3][2] = true;
                pattern[4][2] = true;
                pattern[5][2] = true;
                pattern[6][0] = pattern[6][1] = pattern[6][2] = pattern[6][3] = pattern[6][4] = true;
                break;
            case '2':
                pattern[0][1] = pattern[0][2] = pattern[0][3] = true;
                pattern[1][0] = pattern[1][4] = true;
                pattern[2][4] = true;
                pattern[3][3] = true;
                pattern[4][2] = true;
                pattern[5][1] = true;
                pattern[6][0] = pattern[6][1] = pattern[6][2] = pattern[6][3] = pattern[6][4] = true;
                break;
            case '3':
                pattern[0][1] = pattern[0][2] = pattern[0][3] = true;
                pattern[1][0] = pattern[1][4] = true;
                pattern[2][4] = true;
                pattern[3][2] = pattern[3][3] = true;
                pattern[4][4] = true;
                pattern[5][0] = pattern[5][4] = true;
                pattern[6][1] = pattern[6][2] = pattern[6][3] = true;
                break;
            case '4':
                pattern[0][0] = pattern[0][3] = true;
                pattern[1][0] = pattern[1][3] = true;
                pattern[2][0] = pattern[2][3] = true;
                pattern[3][0] = pattern[3][1] = pattern[3][2] = pattern[3][3] = pattern[3][4] = true;
                pattern[4][3] = true;
                pattern[5][3] = true;
                pattern[6][3] = true;
                break;
            case '5':
                pattern[0][0] = pattern[0][1] = pattern[0][2] = pattern[0][3] = pattern[0][4] = true;
                pattern[1][0] = true;
                pattern[2][0] = true;
                pattern[3][0] = pattern[3][1] = pattern[3][2] = pattern[3][3] = true;
                pattern[4][4] = true;
                pattern[5][0] = pattern[5][4] = true;
                pattern[6][1] = pattern[6][2] = pattern[6][3] = true;
                break;
            case '6':
                pattern[0][1] = pattern[0][2] = pattern[0][3] = true;
                pattern[1][0] = pattern[1][4] = true;
                pattern[2][0] = true;
                pattern[3][0] = pattern[3][1] = pattern[3][2] = pattern[3][3] = true;
                pattern[4][0] = pattern[4][4] = true;
                pattern[5][0] = pattern[5][4] = true;
                pattern[6][1] = pattern[6][2] = pattern[6][3] = true;
                break;
            case '7':
                pattern[0][0] = pattern[0][1] = pattern[0][2] = pattern[0][3] = pattern[0][4] = true;
                pattern[1][4] = true;
                pattern[2][3] = true;
                pattern[3][3] = true;
                pattern[4][2] = true;
                pattern[5][2] = true;
                pattern[6][2] = true;
                break;
            case '8':
                pattern[0][1] = pattern[0][2] = pattern[0][3] = true;
                pattern[1][0] = pattern[1][4] = true;
                pattern[2][0] = pattern[2][4] = true;
                pattern[3][1] = pattern[3][2] = pattern[3][3] = true;
                pattern[4][0] = pattern[4][4] = true;
                pattern[5][0] = pattern[5][4] = true;
                pattern[6][1] = pattern[6][2] = pattern[6][3] = true;
                break;
            case '9':
                pattern[0][1] = pattern[0][2] = pattern[0][3] = true;
                pattern[1][0] = pattern[1][4] = true;
                pattern[2][0] = pattern[2][4] = true;
                pattern[3][1] = pattern[3][2] = pattern[3][3] = pattern[3][4] = true;
                pattern[4][4] = true;
                pattern[5][0] = pattern[5][4] = true;
                pattern[6][1] = pattern[6][2] = pattern[6][3] = true;
                break;
            default:
                // Unknown character - draw a small rectangle
                pattern[2][1] = pattern[2][2] = pattern[2][3] = true;
                pattern[3][1] = pattern[3][2] = pattern[3][3] = true;
                pattern[4][1] = pattern[4][2] = pattern[4][3] = true;
                break;
        }

        // Render the pattern
        for (int row = 0; row < 7; row++) {
            for (int col = 0; col < 5; col++) {
                if (pattern[row][col]) {
                    glBegin(GL_QUADS);
                    glVertex2f(static_cast<float>(x + col), static_cast<float>(y + row));
                    glVertex2f(static_cast<float>(x + col + 1), static_cast<float>(y + row));
                    glVertex2f(static_cast<float>(x + col + 1), static_cast<float>(y + row + 1));
                    glVertex2f(static_cast<float>(x + col), static_cast<float>(y + row + 1));
                    glEnd();
                }
            }
        }
    }

    // Render text using bitmap font
    void renderText(const std::string& text, int x, int y, float r = 1.0f, float g = 1.0f, float b = 1.0f) {
        int currentX = x;
        for (char c : text) {
            renderChar(c, currentX, y, r, g, b);
            currentX += 6; // Character spacing
        }
    }

    // Render a slider
    void renderSlider(Slider& slider) {
        // Slider track
        glColor3f(0.3f, 0.3f, 0.3f);
        glBegin(GL_QUADS);
        glVertex2f(static_cast<float>(slider.x), static_cast<float>(slider.y + slider.height/2 - 2));
        glVertex2f(static_cast<float>(slider.x + slider.width), static_cast<float>(slider.y + slider.height/2 - 2));
        glVertex2f(static_cast<float>(slider.x + slider.width), static_cast<float>(slider.y + slider.height/2 + 2));
        glVertex2f(static_cast<float>(slider.x), static_cast<float>(slider.y + slider.height/2 + 2));
        glEnd();

        // Slider handle
        float ratio = (slider.currentValue - slider.minValue) / (slider.maxValue - slider.minValue);
        int handleX = slider.x + static_cast<int>(ratio * static_cast<float>(slider.width));

        glColor3f(0.8f, 0.8f, 0.8f);
        glBegin(GL_QUADS);
        glVertex2f(static_cast<float>(handleX - 5), static_cast<float>(slider.y));
        glVertex2f(static_cast<float>(handleX + 5), static_cast<float>(slider.y));
        glVertex2f(static_cast<float>(handleX + 5), static_cast<float>(slider.y + slider.height));
        glVertex2f(static_cast<float>(handleX - 5), static_cast<float>(slider.y + slider.height));
        glEnd();

        // Label and value
        renderText(slider.label, slider.x, slider.y - 20, 1.0f, 1.0f, 1.0f);

        // Value text
        std::string valueText = std::to_string(slider.currentValue).substr(0, 4);
        renderText(valueText, slider.x + slider.width + 10, slider.y + slider.height/2 - 6, 0.8f, 0.8f, 0.8f);
    }

    // Render a color picker
    void renderColorPicker(const ColorPicker& picker) {
        // Color square
        glColor3f(*picker.colorR, *picker.colorG, *picker.colorB);
        glBegin(GL_QUADS);
        glVertex2f(static_cast<float>(picker.x), static_cast<float>(picker.y));
        glVertex2f(static_cast<float>(picker.x + picker.size), static_cast<float>(picker.y));
        glVertex2f(static_cast<float>(picker.x + picker.size), static_cast<float>(picker.y + picker.size));
        glVertex2f(static_cast<float>(picker.x), static_cast<float>(picker.y + picker.size));
        glEnd();

        // Border
        glColor3f(1.0f, 1.0f, 1.0f);
        glBegin(GL_LINE_LOOP);
        glVertex2f(static_cast<float>(picker.x), static_cast<float>(picker.y));
        glVertex2f(static_cast<float>(picker.x + picker.size), static_cast<float>(picker.y));
        glVertex2f(static_cast<float>(picker.x + picker.size), static_cast<float>(picker.y + picker.size));
        glVertex2f(static_cast<float>(picker.x), static_cast<float>(picker.y + picker.size));
        glEnd();

        // Label
        renderText(picker.label, picker.x, picker.y - 20, 1.0f, 1.0f, 1.0f);
    }

    // Render a menu button
    void renderButton(const MenuButton& button) {
        // Button background
        if (button.hovered) {
            glColor3f(0.3f, 0.3f, 0.7f); // Highlighted
        } else {
            glColor3f(0.2f, 0.2f, 0.5f); // Normal
        }

        glBegin(GL_QUADS);
        glVertex2f(static_cast<float>(button.x), static_cast<float>(button.y));
        glVertex2f(static_cast<float>(button.x + button.width), static_cast<float>(button.y));
        glVertex2f(static_cast<float>(button.x + button.width), static_cast<float>(button.y + button.height));
        glVertex2f(static_cast<float>(button.x), static_cast<float>(button.y + button.height));
        glEnd();

        // Button border
        glColor3f(1.0f, 1.0f, 1.0f);
        glBegin(GL_LINE_LOOP);
        glVertex2f(static_cast<float>(button.x), static_cast<float>(button.y));
        glVertex2f(static_cast<float>(button.x + button.width), static_cast<float>(button.y));
        glVertex2f(static_cast<float>(button.x + button.width), static_cast<float>(button.y + button.height));
        glVertex2f(static_cast<float>(button.x), static_cast<float>(button.y + button.height));
        glEnd();

        // Button text (properly centered)
        int textWidth = static_cast<int>(button.text.length()) * 6; // 6 pixels per character
        int textX = button.x + (button.width - textWidth) / 2;
        int textY = button.y + button.height / 2 - 3; // Center vertically (7 pixel height / 2)
        renderText(button.text, textX, textY, 1.0f, 1.0f, 1.0f);
    }

    // Render main menu
    void renderMainMenu() {
        // Disable depth testing for 2D rendering
        glDisable(GL_DEPTH_TEST);

        // Set up 2D rendering
        glMatrixMode(GL_PROJECTION);
        glLoadIdentity();
        glOrtho(0, windowWidth, windowHeight, 0, -1, 1);
        glMatrixMode(GL_MODELVIEW);
        glLoadIdentity();

        // Clear with menu background color
        glClearColor(menuBackgroundR, menuBackgroundG, menuBackgroundB, 1.0f);
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

        // Title - properly centered
        std::string title = "HUMANGL - SKELETAL ANIMATION";
        int titleWidth = static_cast<int>(title.length()) * 6; // 6 pixels per character
        renderText(title, windowWidth/2 - titleWidth/2, 100, 1.0f, 1.0f, 0.0f);

        // Create menu buttons
        std::vector<MenuButton> buttons = {
            MenuButton(windowWidth/2 - 100, 200, 200, 50, "START SIMULATION"),
            MenuButton(windowWidth/2 - 100, 270, 200, 50, "SETTINGS"),
            MenuButton(windowWidth/2 - 100, 340, 200, 50, "INSTRUCTIONS"),
            MenuButton(windowWidth/2 - 100, 410, 200, 50, "CREDITS"),
            MenuButton(windowWidth/2 - 100, 480, 200, 50, "EXIT")
        };

        // Update button hover states
        for (auto& button : buttons) {
            button.hovered = button.isClicked(mouseX, mouseY);
        }

        // Render buttons
        for (const auto& button : buttons) {
            renderButton(button);
        }

        // Handle button clicks
        if (mousePressed) {
            if (buttons[0].hovered) currentState = SIMULATION;
            else if (buttons[1].hovered) currentState = SETTINGS_MENU;
            else if (buttons[2].hovered) currentState = INSTRUCTIONS_MENU;
            else if (buttons[3].hovered) currentState = CREDITS_MENU;
            else if (buttons[4].hovered) running = false;
            mousePressed = false; // Reset click
        }
    }

    // Render settings menu
    void renderSettingsMenu() {
        glDisable(GL_DEPTH_TEST);

        glMatrixMode(GL_PROJECTION);
        glLoadIdentity();
        glOrtho(0, windowWidth, windowHeight, 0, -1, 1);
        glMatrixMode(GL_MODELVIEW);
        glLoadIdentity();

        glClearColor(menuBackgroundR, menuBackgroundG, menuBackgroundB, 1.0f);
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

        std::string settingsTitle = "SETTINGS";
        int settingsTitleWidth = static_cast<int>(settingsTitle.length()) * 6;
        renderText(settingsTitle, windowWidth/2 - settingsTitleWidth/2, 100, 1.0f, 1.0f, 0.0f);

        std::string settingsNote = "(Settings will be implemented next)";
        int settingsNoteWidth = static_cast<int>(settingsNote.length()) * 6;
        renderText(settingsNote, windowWidth/2 - settingsNoteWidth/2, 200, 0.8f, 0.8f, 0.8f);

        // Back button
        MenuButton backButton(50, windowHeight - 100, 100, 40, "Back");
        backButton.hovered = backButton.isClicked(mouseX, mouseY);
        renderButton(backButton);

        if (mousePressed && backButton.hovered) {
            currentState = MAIN_MENU;
            mousePressed = false;
        }
    }

    // Render credits menu
    void renderCreditsMenu() {
        glDisable(GL_DEPTH_TEST);

        glMatrixMode(GL_PROJECTION);
        glLoadIdentity();
        glOrtho(0, windowWidth, windowHeight, 0, -1, 1);
        glMatrixMode(GL_MODELVIEW);
        glLoadIdentity();

        glClearColor(menuBackgroundR, menuBackgroundG, menuBackgroundB, 1.0f);
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

        std::string creditsTitle = "CREDITS";
        int creditsTitleWidth = static_cast<int>(creditsTitle.length()) * 6;
        renderText(creditsTitle, windowWidth/2 - creditsTitleWidth/2, 100, 1.0f, 1.0f, 0.0f);

        std::string line1 = "HumanGL Skeletal Animation System";
        int line1Width = static_cast<int>(line1.length()) * 6;
        renderText(line1, windowWidth/2 - line1Width/2, 200, 1.0f, 1.0f, 1.0f);

        std::string line2 = "Developed with C++14 and SDL2";
        int line2Width = static_cast<int>(line2.length()) * 6;
        renderText(line2, windowWidth/2 - line2Width/2, 230, 0.8f, 0.8f, 0.8f);

        std::string line3 = "OpenGL 2.1 Compatible";
        int line3Width = static_cast<int>(line3.length()) * 6;
        renderText(line3, windowWidth/2 - line3Width/2, 260, 0.8f, 0.8f, 0.8f);

        std::string line4 = "Custom Matrix Stack Implementation";
        int line4Width = static_cast<int>(line4.length()) * 6;
        renderText(line4, windowWidth/2 - line4Width/2, 290, 0.8f, 0.8f, 0.8f);

        MenuButton backButton(50, windowHeight - 100, 100, 40, "Back");
        backButton.hovered = backButton.isClicked(mouseX, mouseY);
        renderButton(backButton);

        if (mousePressed && backButton.hovered) {
            currentState = MAIN_MENU;
            mousePressed = false;
        }
    }

    // Render instructions menu
    void renderInstructionsMenu() {
        glDisable(GL_DEPTH_TEST);

        glMatrixMode(GL_PROJECTION);
        glLoadIdentity();
        glOrtho(0, windowWidth, windowHeight, 0, -1, 1);
        glMatrixMode(GL_MODELVIEW);
        glLoadIdentity();

        glClearColor(menuBackgroundR, menuBackgroundG, menuBackgroundB, 1.0f);
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

        std::string instructionsTitle = "INSTRUCTIONS";
        int instructionsTitleWidth = static_cast<int>(instructionsTitle.length()) * 6;
        renderText(instructionsTitle, windowWidth/2 - instructionsTitleWidth/2, 80, 1.0f, 1.0f, 0.0f);

        int y = 130;
        renderText("ANIMATIONS:", 50, y, 1.0f, 1.0f, 0.0f); y += 30;
        renderText("  SPACE - Toggle walking animation", 50, y, 1.0f, 1.0f, 1.0f); y += 25;
        renderText("  P - Jump (single jump)", 50, y, 1.0f, 1.0f, 1.0f); y += 25;
        renderText("  J - Toggle disco dancing", 50, y, 1.0f, 1.0f, 1.0f); y += 25;
        renderText("  K - Toggle kung fu fighting", 50, y, 1.0f, 1.0f, 1.0f); y += 35;

        renderText("CONTROLS:", 50, y, 1.0f, 1.0f, 0.0f); y += 30;
        renderText("  A/D - Rotate torso (all parts follow)", 50, y, 1.0f, 1.0f, 1.0f); y += 25;
        renderText("  W/S - Head up/down, Q/E - Head left/right", 50, y, 1.0f, 1.0f, 1.0f); y += 25;
        renderText("  1-6 - Manual arm controls", 50, y, 1.0f, 1.0f, 1.0f); y += 25;
        renderText("  7-0 - Manual leg controls", 50, y, 1.0f, 1.0f, 1.0f); y += 25;
        renderText("  M - Return to main menu", 50, y, 1.0f, 1.0f, 1.0f); y += 25;
        renderText("  ESC - Exit application", 50, y, 1.0f, 1.0f, 1.0f);

        MenuButton backButton(50, windowHeight - 100, 100, 40, "BACK");
        backButton.hovered = backButton.isClicked(mouseX, mouseY);
        renderButton(backButton);

        if (mousePressed && backButton.hovered) {
            currentState = MAIN_MENU;
            mousePressed = false;
        }
    }

    // Draw eyes on the head to show front direction
    void drawEyes() {
        matrixStack.pushMatrix();

        // Position head above neck (same as head positioning)
        matrixStack.translate(0.0f, 1.5f, 0.0f);

        // Apply head rotations (eyes follow head movement)
        matrixStack.rotateX(headRotationX);
        matrixStack.rotateY(headRotationY);

        // Scale to head size first
        matrixStack.scale(0.9f, 0.9f, 0.9f);

        // Draw left eye
        matrixStack.pushMatrix();
        matrixStack.translate(-0.15f, 0.1f, 0.51f);  // Position on front face, slightly forward
        matrixStack.scale(0.1f, 0.1f, 0.05f);  // Small eye

        glLoadIdentity();
        glTranslatef(0.0f, 0.0f, -8.0f);
        matrixStack.applyToOpenGL();
        drawColoredCube(0.0f, 0.0f, 0.0f);  // Black eye
        matrixStack.popMatrix();

        // Draw right eye
        matrixStack.pushMatrix();
        matrixStack.translate(0.15f, 0.1f, 0.51f);   // Position on front face, slightly forward
        matrixStack.scale(0.1f, 0.1f, 0.05f);   // Small eye

        glLoadIdentity();
        glTranslatef(0.0f, 0.0f, -8.0f);
        matrixStack.applyToOpenGL();
        drawColoredCube(0.0f, 0.0f, 0.0f);  // Black eye
        matrixStack.popMatrix();

        matrixStack.popMatrix();
    }

    // Draw left upper arm
    void drawLeftUpperArm() {
        matrixStack.pushMatrix();

        // Position at left shoulder (connect to shoulder joint)
        matrixStack.translate(-0.8f, 0.6f, 0.0f);

        // Apply upper arm rotations
        matrixStack.rotateX(leftUpperArmX);
        matrixStack.rotateZ(leftUpperArmZ);

        // Move to center of upper arm for proper rotation
        matrixStack.translate(0.0f, -0.6f, 0.0f);

        // Scale to make it look like an upper arm
        matrixStack.scale(0.4f, 1.2f, 0.4f);

        // Apply transformations and draw
        glLoadIdentity();
        glTranslatef(0.0f, 0.0f, -8.0f);
        matrixStack.applyToOpenGL();

        drawColoredCube(0.2f, 0.4f, 0.8f);  // Blue shirt sleeve

        matrixStack.popMatrix();
    }

    // Draw left forearm
    void drawLeftForearm() {
        matrixStack.pushMatrix();

        // Position at left shoulder (same as upper arm start)
        matrixStack.translate(-0.8f, 0.6f, 0.0f);

        // Apply upper arm rotations first
        matrixStack.rotateX(leftUpperArmX);
        matrixStack.rotateZ(leftUpperArmZ);

        // Move to elbow position
        matrixStack.translate(0.0f, -1.2f, 0.0f);

        // Apply forearm rotation
        matrixStack.rotateX(leftForearmX);

        // Move to center of forearm
        matrixStack.translate(0.0f, -0.5f, 0.0f);

        // Scale to make it look like a forearm
        matrixStack.scale(0.35f, 1.0f, 0.35f);

        // Apply transformations and draw
        glLoadIdentity();
        glTranslatef(0.0f, 0.0f, -8.0f);
        matrixStack.applyToOpenGL();

        drawColoredCube(0.8f, 0.6f, 0.4f);  // Skin color for exposed forearm

        matrixStack.popMatrix();
    }

    // Draw right upper arm
    void drawRightUpperArm() {
        matrixStack.pushMatrix();

        // Position at right shoulder (connect to shoulder joint)
        matrixStack.translate(0.8f, 0.6f, 0.0f);

        // Apply upper arm rotations
        matrixStack.rotateX(rightUpperArmX);
        matrixStack.rotateZ(rightUpperArmZ);

        // Move to center of upper arm for proper rotation
        matrixStack.translate(0.0f, -0.6f, 0.0f);

        // Scale to make it look like an upper arm
        matrixStack.scale(0.4f, 1.2f, 0.4f);

        // Apply transformations and draw
        glLoadIdentity();
        glTranslatef(0.0f, 0.0f, -8.0f);
        matrixStack.applyToOpenGL();

        drawColoredCube(0.2f, 0.4f, 0.8f);  // Blue shirt sleeve

        matrixStack.popMatrix();
    }

    // Draw right forearm
    void drawRightForearm() {
        matrixStack.pushMatrix();

        // Position at right shoulder (same as upper arm start)
        matrixStack.translate(0.8f, 0.6f, 0.0f);

        // Apply upper arm rotations first
        matrixStack.rotateX(rightUpperArmX);
        matrixStack.rotateZ(rightUpperArmZ);

        // Move to elbow position
        matrixStack.translate(0.0f, -1.2f, 0.0f);

        // Apply forearm rotation
        matrixStack.rotateX(rightForearmX);

        // Move to center of forearm
        matrixStack.translate(0.0f, -0.5f, 0.0f);

        // Scale to make it look like a forearm
        matrixStack.scale(0.35f, 1.0f, 0.35f);

        // Apply transformations and draw
        glLoadIdentity();
        glTranslatef(0.0f, 0.0f, -8.0f);
        matrixStack.applyToOpenGL();

        drawColoredCube(0.8f, 0.6f, 0.4f);  // Skin color for exposed forearm

        matrixStack.popMatrix();
    }

    // Draw left thigh
    void drawLeftThigh() {
        matrixStack.pushMatrix();

        // Position at left hip
        matrixStack.translate(-0.4f, -0.9f, 0.0f);

        // Apply thigh rotation
        matrixStack.rotateX(leftThighX);

        // Move to center of thigh
        matrixStack.translate(0.0f, -0.7f, 0.0f);

        // Scale to make it look like a thigh
        matrixStack.scale(0.5f, 1.4f, 0.5f);

        // Apply transformations and draw
        glLoadIdentity();
        glTranslatef(0.0f, 0.0f, -8.0f);
        matrixStack.applyToOpenGL();

        drawColoredCube(0.1f, 0.1f, 0.3f);  // Dark blue pants

        matrixStack.popMatrix();
    }

    // Draw left lower leg
    void drawLeftLowerLeg() {
        matrixStack.pushMatrix();

        // Position at left hip (same as thigh start)
        matrixStack.translate(-0.4f, -0.9f, 0.0f);

        // Apply thigh rotation first
        matrixStack.rotateX(leftThighX);

        // Move to knee position
        matrixStack.translate(0.0f, -1.4f, 0.0f);

        // Apply lower leg rotation
        matrixStack.rotateX(leftLowerLegX);

        // Move to center of lower leg
        matrixStack.translate(0.0f, -0.6f, 0.0f);

        // Scale to make it look like a lower leg
        matrixStack.scale(0.4f, 1.2f, 0.4f);

        // Apply transformations and draw
        glLoadIdentity();
        glTranslatef(0.0f, 0.0f, -8.0f);
        matrixStack.applyToOpenGL();

        drawColoredCube(0.1f, 0.1f, 0.3f);  // Dark blue pants

        matrixStack.popMatrix();
    }

    // Draw right thigh
    void drawRightThigh() {
        matrixStack.pushMatrix();

        // Position at right hip
        matrixStack.translate(0.4f, -0.9f, 0.0f);

        // Apply thigh rotation
        matrixStack.rotateX(rightThighX);

        // Move to center of thigh
        matrixStack.translate(0.0f, -0.7f, 0.0f);

        // Scale to make it look like a thigh
        matrixStack.scale(0.5f, 1.4f, 0.5f);

        // Apply transformations and draw
        glLoadIdentity();
        glTranslatef(0.0f, 0.0f, -8.0f);
        matrixStack.applyToOpenGL();

        drawColoredCube(0.1f, 0.1f, 0.3f);  // Dark blue pants

        matrixStack.popMatrix();
    }

    // Draw right lower leg
    void drawRightLowerLeg() {
        matrixStack.pushMatrix();

        // Position at right hip (same as thigh start)
        matrixStack.translate(0.4f, -0.9f, 0.0f);

        // Apply thigh rotation first
        matrixStack.rotateX(rightThighX);

        // Move to knee position
        matrixStack.translate(0.0f, -1.4f, 0.0f);

        // Apply lower leg rotation
        matrixStack.rotateX(rightLowerLegX);

        // Move to center of lower leg
        matrixStack.translate(0.0f, -0.6f, 0.0f);

        // Scale to make it look like a lower leg
        matrixStack.scale(0.4f, 1.2f, 0.4f);

        // Apply transformations and draw
        glLoadIdentity();
        glTranslatef(0.0f, 0.0f, -8.0f);
        matrixStack.applyToOpenGL();

        drawColoredCube(0.1f, 0.1f, 0.3f);  // Dark blue pants

        matrixStack.popMatrix();
    }

    // Update all animations based on current mode
    void updateAnimations() {
        if (currentAnimation == WALKING) {
            animationTime += 0.1f;  // Animation speed

            // Walking cycle using sine waves for natural movement
            float legSwing = sinf(animationTime) * 30.0f;      // Leg swing amplitude

            // More natural arm swing with independent upper arm and forearm movement
            float armSwingPhase = animationTime + M_PI;  // Arms opposite to legs
            float leftArmSwing = sinf(armSwingPhase) * 25.0f;   // Left arm swing
            float rightArmSwing = sinf(armSwingPhase + M_PI) * 25.0f;  // Right arm swing (opposite)

            // Elbow bending during arm swing - forearms bend more when arms swing back
            float leftElbowBend = (sinf(armSwingPhase + M_PI_2) + 1.0f) * 20.0f;  // 0-40 degrees
            float rightElbowBend = (sinf(armSwingPhase + M_PI_2 + M_PI) + 1.0f) * 20.0f;

            // Slight outward arm movement for natural walking
            float armOutward = sinf(animationTime * 2.0f) * 5.0f;

            // Fixed knee flexion - knees bend forward (positive rotation) during swing
            float leftKneeFlexion = (sinf(animationTime * 2.0f) + 1.0f) * 15.0f;   // Left knee
            float rightKneeFlexion = (sinf(animationTime * 2.0f + M_PI) + 1.0f) * 15.0f;  // Right knee (offset)

            // Alternate leg movement (opposite legs move together)
            leftThighX = legSwing;
            rightThighX = -legSwing;

            // Knee flexion during swing phase - FIXED: positive rotation (forward bend)
            leftLowerLegX = leftKneeFlexion;   // Positive = forward bend (natural)
            rightLowerLegX = rightKneeFlexion; // Positive = forward bend (natural)

            // Natural arm movement - upper arms swing, forearms bend independently
            leftUpperArmX = leftArmSwing;      // Forward/back swing
            rightUpperArmX = rightArmSwing;    // Forward/back swing
            leftUpperArmZ = armOutward;        // Slight outward movement
            rightUpperArmZ = -armOutward;      // Slight outward movement

            // Independent forearm movement - bend more when arm swings back
            leftForearmX = -leftElbowBend;     // Negative = bend (elbow flexion)
            rightForearmX = -rightElbowBend;   // Negative = bend (elbow flexion)

            // Reset animation time to prevent overflow
            if (animationTime > 2.0f * M_PI) {
                animationTime = 0.0f;
            }

        } else if (currentAnimation == JUMPING) {
            animationTime += 0.15f;  // Jump animation speed

            // Jump cycle: crouch -> jump -> land
            if (animationTime < M_PI) {
                // Jumping phase (0 to π)
                jumpHeight = sinf(animationTime) * 2.0f;  // Height curve

                // Crouch at start, extend at peak
                float jumpPhase = animationTime / M_PI;
                float legBend = (1.0f - jumpPhase) * 45.0f;  // Start crouched, extend

                // More natural arm movement during jump
                float armRaise = jumpPhase * 70.0f;          // Upper arms go up during jump
                float forearmExtend = jumpPhase * 30.0f;     // Forearms extend during jump
                float armSpread = jumpPhase * 25.0f;         // Arms spread outward

                // Leg positioning for jump
                leftThighX = -legBend * 0.5f;   // Slight forward lean
                rightThighX = -legBend * 0.5f;
                leftLowerLegX = legBend;        // Knees bend forward
                rightLowerLegX = legBend;

                // Natural arm movement - upper arms and forearms move independently
                leftUpperArmX = -armRaise;      // Upper arms swing up and back
                rightUpperArmX = -armRaise;
                leftUpperArmZ = armSpread;      // Arms spread outward for balance
                rightUpperArmZ = -armSpread;

                // Forearms extend during jump (opposite of elbow bending)
                leftForearmX = forearmExtend;   // Positive = extend (straighten elbow)
                rightForearmX = forearmExtend;  // Positive = extend (straighten elbow)

            } else {
                // Landing and reset
                currentAnimation = NONE;
                animationTime = 0.0f;
                jumpHeight = 0.0f;

                // Reset limb positions
                leftThighX = rightThighX = 0.0f;
                leftLowerLegX = rightLowerLegX = 0.0f;
                leftUpperArmX = rightUpperArmX = 0.0f;
                leftUpperArmZ = rightUpperArmZ = 0.0f;
                leftForearmX = rightForearmX = 0.0f;
            }

        } else if (currentAnimation == DANCING) {
            animationTime += 0.12f;  // Dance animation speed

            // Disco dance moves with multiple body parts
            float beat = animationTime * 2.0f;  // Faster beat
            float slowBeat = animationTime * 0.5f;  // Slower movements

            // Head bobbing
            headRotationX = sinf(beat * 4.0f) * 10.0f;
            headRotationY = sinf(beat * 2.0f) * 15.0f;

            // Arm disco moves - alternating high and low
            leftUpperArmX = sinf(beat) * 60.0f - 30.0f;  // Big arm swings
            rightUpperArmX = sinf(beat + M_PI) * 60.0f - 30.0f;  // Opposite
            leftUpperArmZ = sinf(beat * 1.5f) * 45.0f;   // Side to side
            rightUpperArmZ = sinf(beat * 1.5f + M_PI) * 45.0f;

            // Forearm pointing and gestures
            leftForearmX = sinf(beat * 3.0f) * 40.0f - 20.0f;
            rightForearmX = sinf(beat * 3.0f + M_PI) * 40.0f - 20.0f;

            // Hip swaying with legs
            leftThighX = sinf(slowBeat) * 20.0f;
            rightThighX = sinf(slowBeat + M_PI) * 20.0f;

            // Knee bending to the beat
            leftLowerLegX = (sinf(beat * 2.0f) + 1.0f) * 25.0f;
            rightLowerLegX = (sinf(beat * 2.0f + M_PI) + 1.0f) * 25.0f;

            // Reset animation time to prevent overflow
            if (animationTime > 4.0f * M_PI) {
                animationTime = 0.0f;
            }

        } else if (currentAnimation == KUNGFU) {
            animationTime += 0.2f;  // Kung fu animation speed

            // Kung fu fighting sequence
            float phase = fmodf(animationTime, 4.0f * M_PI);  // 4-phase cycle

            if (phase < M_PI) {
                // Phase 1: Ready stance
                leftUpperArmX = -45.0f;   // Guard position
                rightUpperArmX = -45.0f;
                leftUpperArmZ = 30.0f;    // Arms out
                rightUpperArmZ = -30.0f;
                leftForearmX = -60.0f;    // Bent elbows
                rightForearmX = -60.0f;

                // Stable leg stance
                leftThighX = -10.0f;
                rightThighX = -10.0f;
                leftLowerLegX = 20.0f;
                rightLowerLegX = 20.0f;

            } else if (phase < 2.0f * M_PI) {
                // Phase 2: Left punch
                leftUpperArmX = 0.0f;     // Punch forward
                rightUpperArmX = -45.0f;  // Guard
                leftUpperArmZ = 0.0f;     // Straight
                rightUpperArmZ = -30.0f;
                leftForearmX = 0.0f;      // Straight arm
                rightForearmX = -60.0f;   // Bent guard

                // Forward stance
                leftThighX = 20.0f;       // Forward leg
                rightThighX = -30.0f;     // Back leg
                leftLowerLegX = 10.0f;
                rightLowerLegX = 40.0f;

            } else if (phase < 3.0f * M_PI) {
                // Phase 3: Right punch
                leftUpperArmX = -45.0f;   // Guard
                rightUpperArmX = 0.0f;    // Punch forward
                leftUpperArmZ = 30.0f;
                rightUpperArmZ = 0.0f;    // Straight
                leftForearmX = -60.0f;    // Bent guard
                rightForearmX = 0.0f;     // Straight arm

                // Opposite stance
                leftThighX = -30.0f;      // Back leg
                rightThighX = 20.0f;      // Forward leg
                leftLowerLegX = 40.0f;
                rightLowerLegX = 10.0f;

            } else {
                // Phase 4: High kick
                leftUpperArmX = -60.0f;   // Balance arms
                rightUpperArmX = -60.0f;
                leftUpperArmZ = 45.0f;
                rightUpperArmZ = -45.0f;
                leftForearmX = -30.0f;
                rightForearmX = -30.0f;

                // High kick with right leg
                leftThighX = -20.0f;      // Support leg
                rightThighX = 60.0f;      // High kick
                leftLowerLegX = 30.0f;
                rightLowerLegX = -20.0f;  // Kick extension
            }
        }
    }



    // Handle continuous keyboard input for body part rotation
    void handleKeyboardInput() {
        const float rotationSpeed = 2.0f;  // degrees per frame

        // Torso rotation (A/D keys) - can rotate freely
        if (keyboardState[SDL_SCANCODE_A]) {
            torsoRotationY -= rotationSpeed;  // Rotate torso left
        }
        if (keyboardState[SDL_SCANCODE_D]) {
            torsoRotationY += rotationSpeed;  // Rotate torso right
        }

        // Head rotation with realistic limits
        // Head up/down (W/S keys) - limited to natural neck movement
        if (keyboardState[SDL_SCANCODE_W] && headRotationX > -45.0f) {
            headRotationX -= rotationSpeed;  // Head up (limited to 45 degrees up)
        }
        if (keyboardState[SDL_SCANCODE_S] && headRotationX < 30.0f) {
            headRotationX += rotationSpeed;  // Head down (limited to 30 degrees down)
        }

        // Head left/right (Q/E keys) - limited to natural neck turning
        if (keyboardState[SDL_SCANCODE_Q] && headRotationY > -75.0f) {
            headRotationY -= rotationSpeed;  // Head left (limited to 75 degrees left)
        }
        if (keyboardState[SDL_SCANCODE_E] && headRotationY < 75.0f) {
            headRotationY += rotationSpeed;  // Head right (limited to 75 degrees right)
        }

        // Manual limb controls (only when no animation is playing)
        if (currentAnimation == NONE) {
            // Left arm controls (1/2 for upper arm, 3 for forearm)
            if (keyboardState[SDL_SCANCODE_1]) {
                leftUpperArmX += rotationSpeed;  // Left upper arm forward
            }
            if (keyboardState[SDL_SCANCODE_2]) {
                leftUpperArmZ += rotationSpeed;  // Left upper arm out
            }
            if (keyboardState[SDL_SCANCODE_3] && leftForearmX > -120.0f) {
                leftForearmX -= rotationSpeed;  // Left forearm bend (limited)
            }

            // Right arm controls (4/5 for upper arm, 6 for forearm)
            if (keyboardState[SDL_SCANCODE_4]) {
                rightUpperArmX += rotationSpeed;  // Right upper arm forward
            }
            if (keyboardState[SDL_SCANCODE_5]) {
                rightUpperArmZ -= rotationSpeed;  // Right upper arm out
            }
            if (keyboardState[SDL_SCANCODE_6] && rightForearmX > -120.0f) {
                rightForearmX -= rotationSpeed;  // Right forearm bend (limited)
            }

            // Left leg controls (7 for thigh, 8 for lower leg)
            if (keyboardState[SDL_SCANCODE_7] && leftThighX > -90.0f && leftThighX < 45.0f) {
                leftThighX += rotationSpeed;  // Left thigh forward/back
            }
            if (keyboardState[SDL_SCANCODE_8] && leftLowerLegX > -120.0f) {
                leftLowerLegX -= rotationSpeed;  // Left lower leg bend (limited)
            }

            // Right leg controls (9 for thigh, 0 for lower leg)
            if (keyboardState[SDL_SCANCODE_9] && rightThighX > -90.0f && rightThighX < 45.0f) {
                rightThighX += rotationSpeed;  // Right thigh forward/back
            }
            if (keyboardState[SDL_SCANCODE_0] && rightLowerLegX > -120.0f) {
                rightLowerLegX -= rotationSpeed;  // Right lower leg bend (limited)
            }
        }

        // Keep torso rotation in reasonable range (can spin freely)
        if (torsoRotationY > 360.0f) torsoRotationY -= 360.0f;
        if (torsoRotationY < -360.0f) torsoRotationY += 360.0f;

        // Clamp head rotations to realistic limits (safety check)
        if (headRotationX > 30.0f) headRotationX = 30.0f;
        if (headRotationX < -45.0f) headRotationX = -45.0f;
        if (headRotationY > 75.0f) headRotationY = 75.0f;
        if (headRotationY < -75.0f) headRotationY = -75.0f;

        // Clamp limb rotations to realistic limits
        if (leftForearmX < -120.0f) leftForearmX = -120.0f;
        if (rightForearmX < -120.0f) rightForearmX = -120.0f;
        if (leftLowerLegX < -120.0f) leftLowerLegX = -120.0f;
        if (rightLowerLegX < -120.0f) rightLowerLegX = -120.0f;
    }

public:
    Application() : window(nullptr), glContext(nullptr), running(false),
                   windowWidth(800), windowHeight(600),
                   currentState(MAIN_MENU), mouseX(0), mouseY(0), mousePressed(false),
                   menuBackgroundR(0.1f), menuBackgroundG(0.1f), menuBackgroundB(0.2f),
                   torsoRotationY(0.0f), headRotationX(0.0f), headRotationY(0.0f),
                   leftUpperArmX(0.0f), leftUpperArmZ(0.0f), leftForearmX(0.0f),
                   rightUpperArmX(0.0f), rightUpperArmZ(0.0f), rightForearmX(0.0f),
                   leftThighX(0.0f), leftLowerLegX(0.0f),
                   rightThighX(0.0f), rightLowerLegX(0.0f),
                   currentAnimation(NONE), animationTime(0.0f), jumpHeight(0.0f),
                   keyboardState(nullptr) {}

    ~Application() {
        cleanup();
    }

    bool initialize() {
        // Initialize SDL
        if (SDL_Init(SDL_INIT_VIDEO) < 0) {
            std::cerr << "Failed to initialize SDL: " << SDL_GetError() << std::endl;
            return false;
        }

        // Set OpenGL attributes (using compatibility profile for immediate mode)
        SDL_GL_SetAttribute(SDL_GL_CONTEXT_MAJOR_VERSION, 2);
        SDL_GL_SetAttribute(SDL_GL_CONTEXT_MINOR_VERSION, 1);
        SDL_GL_SetAttribute(SDL_GL_DOUBLEBUFFER, 1);
        SDL_GL_SetAttribute(SDL_GL_DEPTH_SIZE, 24);

        // Create window
        window = SDL_CreateWindow(
            "HumanGL - Skeletal Animation System",
            SDL_WINDOWPOS_CENTERED,
            SDL_WINDOWPOS_CENTERED,
            windowWidth,
            windowHeight,
            SDL_WINDOW_OPENGL | SDL_WINDOW_SHOWN
        );

        if (!window) {
            std::cerr << "Failed to create window: " << SDL_GetError() << std::endl;
            return false;
        }

        // Create OpenGL context
        glContext = SDL_GL_CreateContext(window);
        if (!glContext) {
            std::cerr << "Failed to create OpenGL context: " << SDL_GetError() << std::endl;
            return false;
        }

        // Enable VSync
        SDL_GL_SetSwapInterval(1);

        // Initialize OpenGL settings
        glViewport(0, 0, windowWidth, windowHeight);
        glClearColor(0.2f, 0.3f, 0.4f, 1.0f);  // Nice blue-gray background
        glEnable(GL_DEPTH_TEST);

        std::cout << "OpenGL Version: " << glGetString(GL_VERSION) << std::endl;
        std::cout << "OpenGL Renderer: " << glGetString(GL_RENDERER) << std::endl;

        // Set up 3D perspective projection
        setupPerspective();

        // Get keyboard state
        keyboardState = SDL_GetKeyboardState(nullptr);

        running = true;
        return true;
    }

    void handleEvents() {
        SDL_Event event;
        while (SDL_PollEvent(&event)) {
            switch (event.type) {
                case SDL_QUIT:
                    running = false;
                    break;
                case SDL_MOUSEMOTION:
                    mouseX = event.motion.x;
                    mouseY = event.motion.y;
                    break;
                case SDL_MOUSEBUTTONDOWN:
                    if (event.button.button == SDL_BUTTON_LEFT) {
                        mousePressed = true;
                    }
                    break;
                case SDL_KEYDOWN:
                    if (event.key.keysym.sym == SDLK_ESCAPE) {
                        running = false;
                    }
                    if (event.key.keysym.sym == SDLK_m && currentState == SIMULATION) {
                        currentState = MAIN_MENU;
                        // Reset animation when returning to menu
                        currentAnimation = NONE;
                        animationTime = 0.0f;
                        jumpHeight = 0.0f;
                        // Reset limb positions
                        leftThighX = rightThighX = 0.0f;
                        leftLowerLegX = rightLowerLegX = 0.0f;
                        leftUpperArmX = rightUpperArmX = 0.0f;
                        leftUpperArmZ = rightUpperArmZ = 0.0f;
                        leftForearmX = rightForearmX = 0.0f;
                        headRotationX = headRotationY = 0.0f;
                        torsoRotationY = 0.0f;
                    }
                    // Animation controls only work in simulation mode
                    if (currentState == SIMULATION) {
                        if (event.key.keysym.sym == SDLK_SPACE) {
                            // Toggle walking mode
                            if (currentAnimation == WALKING) {
                            currentAnimation = NONE;
                            // Reset limb positions
                            leftThighX = rightThighX = 0.0f;
                            leftLowerLegX = rightLowerLegX = 0.0f;
                            leftUpperArmX = rightUpperArmX = 0.0f;
                            leftUpperArmZ = rightUpperArmZ = 0.0f;
                            leftForearmX = rightForearmX = 0.0f;
                            animationTime = 0.0f;
                        } else if (currentAnimation == NONE) {
                            currentAnimation = WALKING;
                            animationTime = 0.0f;
                        }
                    }
                    if (event.key.keysym.sym == SDLK_p) {
                        if (currentAnimation == NONE) {
                            currentAnimation = JUMPING;
                            animationTime = 0.0f;
                            jumpHeight = 0.0f;
                        }
                    }
                    if (event.key.keysym.sym == SDLK_j) {  // J for dancing
                        if (currentAnimation == DANCING) {
                            currentAnimation = NONE;
                            // Reset limb positions
                            leftThighX = rightThighX = 0.0f;
                            leftLowerLegX = rightLowerLegX = 0.0f;
                            leftUpperArmX = rightUpperArmX = 0.0f;
                            leftUpperArmZ = rightUpperArmZ = 0.0f;
                            leftForearmX = rightForearmX = 0.0f;
                            headRotationX = headRotationY = 0.0f;
                            animationTime = 0.0f;
                        } else if (currentAnimation == NONE) {
                            currentAnimation = DANCING;
                            animationTime = 0.0f;
                        }
                    }
                    if (event.key.keysym.sym == SDLK_k) {  // K for kung fu
                        if (currentAnimation == KUNGFU) {
                            currentAnimation = NONE;
                            // Reset limb positions
                            leftThighX = rightThighX = 0.0f;
                            leftLowerLegX = rightLowerLegX = 0.0f;
                            leftUpperArmX = rightUpperArmX = 0.0f;
                            leftUpperArmZ = rightUpperArmZ = 0.0f;
                            leftForearmX = rightForearmX = 0.0f;
                            animationTime = 0.0f;
                        } else if (currentAnimation == NONE) {
                            currentAnimation = KUNGFU;
                            animationTime = 0.0f;
                        }
                    }
                    } // End simulation mode check
                    break;
                case SDL_WINDOWEVENT:
                    if (event.window.event == SDL_WINDOWEVENT_RESIZED) {
                        windowWidth = event.window.data1;
                        windowHeight = event.window.data2;
                        glViewport(0, 0, windowWidth, windowHeight);
                        setupPerspective();  // Update perspective on resize
                    }
                    break;
            }
        }
    }

    void render() {
        switch (currentState) {
            case MAIN_MENU:
                renderMainMenu();
                break;
            case SETTINGS_MENU:
                renderSettingsMenu();
                break;
            case CREDITS_MENU:
                renderCreditsMenu();
                break;
            case INSTRUCTIONS_MENU:
                renderInstructionsMenu();
                break;
            case SIMULATION:
                renderSimulation();
                break;
        }

        // Swap buffers
        SDL_GL_SwapWindow(window);
    }

    void renderSimulation() {
        // Enable depth testing for 3D rendering
        glEnable(GL_DEPTH_TEST);

        // Set up 3D rendering
        glMatrixMode(GL_PROJECTION);
        glLoadIdentity();
        GLdouble fovy = 45.0;
        GLdouble aspect = static_cast<GLdouble>(windowWidth) / static_cast<GLdouble>(windowHeight);
        GLdouble nearPlane = 0.1;
        GLdouble farPlane = 100.0;
        gluPerspective(fovy, aspect, nearPlane, farPlane);
        glMatrixMode(GL_MODELVIEW);
        glLoadIdentity();

        // Clear the screen with simulation background
        glClearColor(0.2f, 0.3f, 0.4f, 1.0f);
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

        // Reset matrix stack
        matrixStack.loadIdentity();

        // Apply jump height (affects entire body)
        matrixStack.translate(0.0f, jumpHeight, 0.0f);

        // Apply torso rotation (this affects all body parts)
        matrixStack.rotateY(torsoRotationY);

        // Draw body parts in hierarchical order
        drawTorso();           // Base of hierarchy
        drawNeck();            // Connected to torso
        drawHead();            // Connected to neck
        drawEyes();            // Eyes on the head (shows front direction)

        // Draw shoulder joints
        drawLeftShoulder();    // Left shoulder joint
        drawRightShoulder();   // Right shoulder joint

        // Draw arms (connected to shoulders)
        drawLeftUpperArm();    // Left upper arm
        drawLeftForearm();     // Left forearm (follows upper arm)
        drawRightUpperArm();   // Right upper arm
        drawRightForearm();    // Right forearm (follows upper arm)

        // Draw legs (connected to torso)
        drawLeftThigh();       // Left thigh
        drawLeftLowerLeg();    // Left lower leg (follows thigh)
        drawRightThigh();      // Right thigh
        drawRightLowerLeg();   // Right lower leg (follows thigh)
    }

    void run() {
        if (!initialize()) {
            return;
        }

        std::cout << "=== HumanGL Skeletal Animation System ===" << std::endl;
        std::cout << "Menu-driven application with full skeletal animation!" << std::endl;
        std::cout << "Use mouse to navigate menus." << std::endl;
        std::cout << "Press ESC to exit at any time." << std::endl;

        while (running) {
            handleEvents();

            // Only update animations and handle input in simulation mode
            if (currentState == SIMULATION) {
                updateAnimations();        // Update all animations
                handleKeyboardInput();     // Handle continuous keyboard input
            }

            render();

            // Small delay to prevent excessive CPU usage
            SDL_Delay(16); // ~60 FPS
        }
    }

    void cleanup() {
        if (glContext) {
            SDL_GL_DeleteContext(glContext);
            glContext = nullptr;
        }
        if (window) {
            SDL_DestroyWindow(window);
            window = nullptr;
        }
        SDL_Quit();
    }
};

int main(int argc, char* argv[]) {
    (void)argc;  // Suppress unused parameter warning
    (void)argv;  // Suppress unused parameter warning
    
    Application app;
    app.run();
    
    return 0;
}
