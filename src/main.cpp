#define GL_SILENCE_DEPRECATION
#include <SDL2/SDL.h>
#include <SDL2/SDL_opengl.h>
#include <OpenGL/gl.h>
#include <OpenGL/glu.h>
#include <iostream>
#include <cstdlib>
#include <cmath>
#include <vector>
#include <stack>

// Simple 4x4 Matrix class for transformations
class Matrix4 {
public:
    float m[16];  // Column-major order (OpenGL style)

    Matrix4() {
        loadIdentity();
    }

    void loadIdentity() {
        for (int i = 0; i < 16; i++) {
            m[i] = 0.0f;
        }
        m[0] = m[5] = m[10] = m[15] = 1.0f;  // Diagonal elements
    }

    void translate(float x, float y, float z) {
        Matrix4 trans;
        trans.m[12] = x;
        trans.m[13] = y;
        trans.m[14] = z;
        *this = *this * trans;
    }

    void rotateX(float degrees) {
        float rad = degrees * M_PI / 180.0f;
        float c = cosf(rad);
        float s = sinf(rad);

        Matrix4 rot;
        rot.m[5] = c;   rot.m[6] = s;
        rot.m[9] = -s;  rot.m[10] = c;
        *this = *this * rot;
    }

    void rotateY(float degrees) {
        float rad = degrees * M_PI / 180.0f;
        float c = cosf(rad);
        float s = sinf(rad);

        Matrix4 rot;
        rot.m[0] = c;   rot.m[2] = -s;
        rot.m[8] = s;   rot.m[10] = c;
        *this = *this * rot;
    }

    void rotateZ(float degrees) {
        float rad = degrees * M_PI / 180.0f;
        float c = cosf(rad);
        float s = sinf(rad);

        Matrix4 rot;
        rot.m[0] = c;   rot.m[1] = s;
        rot.m[4] = -s;  rot.m[5] = c;
        *this = *this * rot;
    }

    void scale(float x, float y, float z) {
        Matrix4 scl;
        scl.m[0] = x;
        scl.m[5] = y;
        scl.m[10] = z;
        *this = *this * scl;
    }

    Matrix4 operator*(const Matrix4& other) const {
        Matrix4 result;
        for (int i = 0; i < 4; i++) {
            for (int j = 0; j < 4; j++) {
                result.m[i * 4 + j] = 0.0f;
                for (int k = 0; k < 4; k++) {
                    result.m[i * 4 + j] += m[k * 4 + j] * other.m[i * 4 + k];
                }
            }
        }
        return result;
    }

    void applyToOpenGL() const {
        glMultMatrixf(m);
    }
};

// Matrix Stack for hierarchical transformations
class MatrixStack {
private:
    std::stack<Matrix4> stack;
    Matrix4 current;

public:
    MatrixStack() {
        current.loadIdentity();
    }

    void pushMatrix() {
        stack.push(current);
    }

    void popMatrix() {
        if (!stack.empty()) {
            current = stack.top();
            stack.pop();
        }
    }

    void loadIdentity() {
        current.loadIdentity();
    }

    void translate(float x, float y, float z) {
        current.translate(x, y, z);
    }

    void rotateX(float degrees) {
        current.rotateX(degrees);
    }

    void rotateY(float degrees) {
        current.rotateY(degrees);
    }

    void rotateZ(float degrees) {
        current.rotateZ(degrees);
    }

    void scale(float x, float y, float z) {
        current.scale(x, y, z);
    }

    void applyToOpenGL() const {
        current.applyToOpenGL();
    }
};

class Application {
private:
    SDL_Window* window;
    SDL_GLContext glContext;
    bool running;
    int windowWidth;
    int windowHeight;

    // Matrix stack for hierarchical transformations
    MatrixStack matrixStack;

    // Body part rotations
    float torsoRotationY;
    float headRotationX;
    float headRotationY;

    // Arm rotations (left and right)
    float leftUpperArmX, leftUpperArmZ;
    float leftForearmX;
    float rightUpperArmX, rightUpperArmZ;
    float rightForearmX;

    // Leg rotations (left and right)
    float leftThighX;
    float leftLowerLegX;
    float rightThighX;
    float rightLowerLegX;

    // Keyboard state
    const Uint8* keyboardState;

    // Helper method to set up 3D perspective projection
    void setupPerspective() {
        glMatrixMode(GL_PROJECTION);
        glLoadIdentity();

        // Set up perspective projection
        // gluPerspective(fovy, aspect, near, far)
        GLdouble fovy = 45.0;  // Field of view in degrees
        GLdouble aspect = static_cast<GLdouble>(windowWidth) / static_cast<GLdouble>(windowHeight);
        GLdouble nearPlane = 0.1;
        GLdouble farPlane = 100.0;
        gluPerspective(fovy, aspect, nearPlane, farPlane);

        glMatrixMode(GL_MODELVIEW);
        glLoadIdentity();
    }

    // Draw a 1x1x1 unit cube at the origin (as required by project)
    void drawUnitCube() {
        glBegin(GL_QUADS);

        // Front face (red)
        glColor3f(1.0f, 0.0f, 0.0f);
        glVertex3f(-0.5f, -0.5f,  0.5f);  // Bottom left
        glVertex3f( 0.5f, -0.5f,  0.5f);  // Bottom right
        glVertex3f( 0.5f,  0.5f,  0.5f);  // Top right
        glVertex3f(-0.5f,  0.5f,  0.5f);  // Top left

        // Back face (green)
        glColor3f(0.0f, 1.0f, 0.0f);
        glVertex3f(-0.5f, -0.5f, -0.5f);  // Bottom left
        glVertex3f(-0.5f,  0.5f, -0.5f);  // Top left
        glVertex3f( 0.5f,  0.5f, -0.5f);  // Top right
        glVertex3f( 0.5f, -0.5f, -0.5f);  // Bottom right

        // Top face (blue)
        glColor3f(0.0f, 0.0f, 1.0f);
        glVertex3f(-0.5f,  0.5f, -0.5f);  // Back left
        glVertex3f(-0.5f,  0.5f,  0.5f);  // Front left
        glVertex3f( 0.5f,  0.5f,  0.5f);  // Front right
        glVertex3f( 0.5f,  0.5f, -0.5f);  // Back right

        // Bottom face (yellow)
        glColor3f(1.0f, 1.0f, 0.0f);
        glVertex3f(-0.5f, -0.5f, -0.5f);  // Back left
        glVertex3f( 0.5f, -0.5f, -0.5f);  // Back right
        glVertex3f( 0.5f, -0.5f,  0.5f);  // Front right
        glVertex3f(-0.5f, -0.5f,  0.5f);  // Front left

        // Right face (magenta)
        glColor3f(1.0f, 0.0f, 1.0f);
        glVertex3f( 0.5f, -0.5f, -0.5f);  // Back bottom
        glVertex3f( 0.5f,  0.5f, -0.5f);  // Back top
        glVertex3f( 0.5f,  0.5f,  0.5f);  // Front top
        glVertex3f( 0.5f, -0.5f,  0.5f);  // Front bottom

        // Left face (cyan)
        glColor3f(0.0f, 1.0f, 1.0f);
        glVertex3f(-0.5f, -0.5f, -0.5f);  // Back bottom
        glVertex3f(-0.5f, -0.5f,  0.5f);  // Front bottom
        glVertex3f(-0.5f,  0.5f,  0.5f);  // Front top
        glVertex3f(-0.5f,  0.5f, -0.5f);  // Back top

        glEnd();
    }

    // Draw the torso (scaled unit cube)
    void drawTorso() {
        matrixStack.pushMatrix();

        // Scale to make it look like a torso (wider and taller)
        matrixStack.scale(1.2f, 1.8f, 0.8f);

        // Apply transformations and draw
        glLoadIdentity();
        glTranslatef(0.0f, 0.0f, -8.0f);  // Move back to see the model
        matrixStack.applyToOpenGL();

        drawUnitCube();

        matrixStack.popMatrix();
    }

    // Draw a more pronounced neck
    void drawNeck() {
        matrixStack.pushMatrix();

        // Position neck at top of torso
        matrixStack.translate(0.0f, 1.1f, 0.0f);

        // Apply head rotations to neck too (neck follows head movement)
        matrixStack.rotateX(headRotationX * 0.3f);  // Neck moves less than head
        matrixStack.rotateY(headRotationY * 0.3f);

        // Scale to make a more visible neck (taller and slightly wider)
        matrixStack.scale(0.5f, 0.6f, 0.5f);

        // Apply transformations and draw
        glLoadIdentity();
        glTranslatef(0.0f, 0.0f, -8.0f);
        matrixStack.applyToOpenGL();

        // Draw neck with a different color (skin tone)
        glBegin(GL_QUADS);
        glColor3f(0.8f, 0.6f, 0.4f);  // Light brown/skin color

        // Simple cube for neck (all faces same color)
        // Front face
        glVertex3f(-0.5f, -0.5f,  0.5f);
        glVertex3f( 0.5f, -0.5f,  0.5f);
        glVertex3f( 0.5f,  0.5f,  0.5f);
        glVertex3f(-0.5f,  0.5f,  0.5f);

        // Back face
        glVertex3f(-0.5f, -0.5f, -0.5f);
        glVertex3f(-0.5f,  0.5f, -0.5f);
        glVertex3f( 0.5f,  0.5f, -0.5f);
        glVertex3f( 0.5f, -0.5f, -0.5f);

        // Top face
        glVertex3f(-0.5f,  0.5f, -0.5f);
        glVertex3f(-0.5f,  0.5f,  0.5f);
        glVertex3f( 0.5f,  0.5f,  0.5f);
        glVertex3f( 0.5f,  0.5f, -0.5f);

        // Bottom face
        glVertex3f(-0.5f, -0.5f, -0.5f);
        glVertex3f( 0.5f, -0.5f, -0.5f);
        glVertex3f( 0.5f, -0.5f,  0.5f);
        glVertex3f(-0.5f, -0.5f,  0.5f);

        // Right face
        glVertex3f( 0.5f, -0.5f, -0.5f);
        glVertex3f( 0.5f,  0.5f, -0.5f);
        glVertex3f( 0.5f,  0.5f,  0.5f);
        glVertex3f( 0.5f, -0.5f,  0.5f);

        // Left face
        glVertex3f(-0.5f, -0.5f, -0.5f);
        glVertex3f(-0.5f, -0.5f,  0.5f);
        glVertex3f(-0.5f,  0.5f,  0.5f);
        glVertex3f(-0.5f,  0.5f, -0.5f);

        glEnd();

        matrixStack.popMatrix();
    }

    // Draw the head (smaller unit cube, positioned relative to torso)
    void drawHead() {
        matrixStack.pushMatrix();

        // Position head above neck
        matrixStack.translate(0.0f, 1.5f, 0.0f);  // Position above neck

        // Apply head rotations
        matrixStack.rotateX(headRotationX);
        matrixStack.rotateY(headRotationY);

        // Scale to make it look like a head (smaller but proportional)
        matrixStack.scale(0.9f, 0.9f, 0.9f);

        // Apply transformations and draw
        glLoadIdentity();
        glTranslatef(0.0f, 0.0f, -8.0f);  // Move back to see the model
        matrixStack.applyToOpenGL();

        drawUnitCube();

        matrixStack.popMatrix();
    }

    // Draw left upper arm
    void drawLeftUpperArm() {
        matrixStack.pushMatrix();

        // Position at left shoulder
        matrixStack.translate(-1.0f, 0.6f, 0.0f);

        // Apply upper arm rotations
        matrixStack.rotateX(leftUpperArmX);
        matrixStack.rotateZ(leftUpperArmZ);

        // Move to center of upper arm for proper rotation
        matrixStack.translate(0.0f, -0.6f, 0.0f);

        // Scale to make it look like an upper arm
        matrixStack.scale(0.4f, 1.2f, 0.4f);

        // Apply transformations and draw
        glLoadIdentity();
        glTranslatef(0.0f, 0.0f, -8.0f);
        matrixStack.applyToOpenGL();

        drawUnitCube();

        matrixStack.popMatrix();
    }

    // Draw left forearm
    void drawLeftForearm() {
        matrixStack.pushMatrix();

        // Position at left shoulder (same as upper arm start)
        matrixStack.translate(-1.0f, 0.6f, 0.0f);

        // Apply upper arm rotations first
        matrixStack.rotateX(leftUpperArmX);
        matrixStack.rotateZ(leftUpperArmZ);

        // Move to elbow position
        matrixStack.translate(0.0f, -1.2f, 0.0f);

        // Apply forearm rotation
        matrixStack.rotateX(leftForearmX);

        // Move to center of forearm
        matrixStack.translate(0.0f, -0.5f, 0.0f);

        // Scale to make it look like a forearm
        matrixStack.scale(0.35f, 1.0f, 0.35f);

        // Apply transformations and draw
        glLoadIdentity();
        glTranslatef(0.0f, 0.0f, -8.0f);
        matrixStack.applyToOpenGL();

        drawUnitCube();

        matrixStack.popMatrix();
    }

    // Draw right upper arm
    void drawRightUpperArm() {
        matrixStack.pushMatrix();

        // Position at right shoulder
        matrixStack.translate(1.0f, 0.6f, 0.0f);

        // Apply upper arm rotations
        matrixStack.rotateX(rightUpperArmX);
        matrixStack.rotateZ(rightUpperArmZ);

        // Move to center of upper arm for proper rotation
        matrixStack.translate(0.0f, -0.6f, 0.0f);

        // Scale to make it look like an upper arm
        matrixStack.scale(0.4f, 1.2f, 0.4f);

        // Apply transformations and draw
        glLoadIdentity();
        glTranslatef(0.0f, 0.0f, -8.0f);
        matrixStack.applyToOpenGL();

        drawUnitCube();

        matrixStack.popMatrix();
    }

    // Draw right forearm
    void drawRightForearm() {
        matrixStack.pushMatrix();

        // Position at right shoulder (same as upper arm start)
        matrixStack.translate(1.0f, 0.6f, 0.0f);

        // Apply upper arm rotations first
        matrixStack.rotateX(rightUpperArmX);
        matrixStack.rotateZ(rightUpperArmZ);

        // Move to elbow position
        matrixStack.translate(0.0f, -1.2f, 0.0f);

        // Apply forearm rotation
        matrixStack.rotateX(rightForearmX);

        // Move to center of forearm
        matrixStack.translate(0.0f, -0.5f, 0.0f);

        // Scale to make it look like a forearm
        matrixStack.scale(0.35f, 1.0f, 0.35f);

        // Apply transformations and draw
        glLoadIdentity();
        glTranslatef(0.0f, 0.0f, -8.0f);
        matrixStack.applyToOpenGL();

        drawUnitCube();

        matrixStack.popMatrix();
    }

    // Draw left thigh
    void drawLeftThigh() {
        matrixStack.pushMatrix();

        // Position at left hip
        matrixStack.translate(-0.4f, -0.9f, 0.0f);

        // Apply thigh rotation
        matrixStack.rotateX(leftThighX);

        // Move to center of thigh
        matrixStack.translate(0.0f, -0.7f, 0.0f);

        // Scale to make it look like a thigh
        matrixStack.scale(0.5f, 1.4f, 0.5f);

        // Apply transformations and draw
        glLoadIdentity();
        glTranslatef(0.0f, 0.0f, -8.0f);
        matrixStack.applyToOpenGL();

        drawUnitCube();

        matrixStack.popMatrix();
    }

    // Draw left lower leg
    void drawLeftLowerLeg() {
        matrixStack.pushMatrix();

        // Position at left hip (same as thigh start)
        matrixStack.translate(-0.4f, -0.9f, 0.0f);

        // Apply thigh rotation first
        matrixStack.rotateX(leftThighX);

        // Move to knee position
        matrixStack.translate(0.0f, -1.4f, 0.0f);

        // Apply lower leg rotation
        matrixStack.rotateX(leftLowerLegX);

        // Move to center of lower leg
        matrixStack.translate(0.0f, -0.6f, 0.0f);

        // Scale to make it look like a lower leg
        matrixStack.scale(0.4f, 1.2f, 0.4f);

        // Apply transformations and draw
        glLoadIdentity();
        glTranslatef(0.0f, 0.0f, -8.0f);
        matrixStack.applyToOpenGL();

        drawUnitCube();

        matrixStack.popMatrix();
    }

    // Draw right thigh
    void drawRightThigh() {
        matrixStack.pushMatrix();

        // Position at right hip
        matrixStack.translate(0.4f, -0.9f, 0.0f);

        // Apply thigh rotation
        matrixStack.rotateX(rightThighX);

        // Move to center of thigh
        matrixStack.translate(0.0f, -0.7f, 0.0f);

        // Scale to make it look like a thigh
        matrixStack.scale(0.5f, 1.4f, 0.5f);

        // Apply transformations and draw
        glLoadIdentity();
        glTranslatef(0.0f, 0.0f, -8.0f);
        matrixStack.applyToOpenGL();

        drawUnitCube();

        matrixStack.popMatrix();
    }

    // Draw right lower leg
    void drawRightLowerLeg() {
        matrixStack.pushMatrix();

        // Position at right hip (same as thigh start)
        matrixStack.translate(0.4f, -0.9f, 0.0f);

        // Apply thigh rotation first
        matrixStack.rotateX(rightThighX);

        // Move to knee position
        matrixStack.translate(0.0f, -1.4f, 0.0f);

        // Apply lower leg rotation
        matrixStack.rotateX(rightLowerLegX);

        // Move to center of lower leg
        matrixStack.translate(0.0f, -0.6f, 0.0f);

        // Scale to make it look like a lower leg
        matrixStack.scale(0.4f, 1.2f, 0.4f);

        // Apply transformations and draw
        glLoadIdentity();
        glTranslatef(0.0f, 0.0f, -8.0f);
        matrixStack.applyToOpenGL();

        drawUnitCube();

        matrixStack.popMatrix();
    }

    // Handle continuous keyboard input for body part rotation
    void handleKeyboardInput() {
        const float rotationSpeed = 2.0f;  // degrees per frame

        // Torso rotation (A/D keys) - can rotate freely
        if (keyboardState[SDL_SCANCODE_A]) {
            torsoRotationY -= rotationSpeed;  // Rotate torso left
        }
        if (keyboardState[SDL_SCANCODE_D]) {
            torsoRotationY += rotationSpeed;  // Rotate torso right
        }

        // Head rotation with realistic limits
        // Head up/down (W/S keys) - limited to natural neck movement
        if (keyboardState[SDL_SCANCODE_W] && headRotationX > -45.0f) {
            headRotationX -= rotationSpeed;  // Head up (limited to 45 degrees up)
        }
        if (keyboardState[SDL_SCANCODE_S] && headRotationX < 30.0f) {
            headRotationX += rotationSpeed;  // Head down (limited to 30 degrees down)
        }

        // Head left/right (Q/E keys) - limited to natural neck turning
        if (keyboardState[SDL_SCANCODE_Q] && headRotationY > -75.0f) {
            headRotationY -= rotationSpeed;  // Head left (limited to 75 degrees left)
        }
        if (keyboardState[SDL_SCANCODE_E] && headRotationY < 75.0f) {
            headRotationY += rotationSpeed;  // Head right (limited to 75 degrees right)
        }

        // Left arm controls (1/2 for upper arm, 3 for forearm)
        if (keyboardState[SDL_SCANCODE_1]) {
            leftUpperArmX += rotationSpeed;  // Left upper arm forward
        }
        if (keyboardState[SDL_SCANCODE_2]) {
            leftUpperArmZ += rotationSpeed;  // Left upper arm out
        }
        if (keyboardState[SDL_SCANCODE_3] && leftForearmX > -120.0f) {
            leftForearmX -= rotationSpeed;  // Left forearm bend (limited)
        }

        // Right arm controls (4/5 for upper arm, 6 for forearm)
        if (keyboardState[SDL_SCANCODE_4]) {
            rightUpperArmX += rotationSpeed;  // Right upper arm forward
        }
        if (keyboardState[SDL_SCANCODE_5]) {
            rightUpperArmZ -= rotationSpeed;  // Right upper arm out
        }
        if (keyboardState[SDL_SCANCODE_6] && rightForearmX > -120.0f) {
            rightForearmX -= rotationSpeed;  // Right forearm bend (limited)
        }

        // Left leg controls (7 for thigh, 8 for lower leg)
        if (keyboardState[SDL_SCANCODE_7] && leftThighX > -90.0f && leftThighX < 45.0f) {
            leftThighX += rotationSpeed;  // Left thigh forward/back
        }
        if (keyboardState[SDL_SCANCODE_8] && leftLowerLegX > -120.0f) {
            leftLowerLegX -= rotationSpeed;  // Left lower leg bend (limited)
        }

        // Right leg controls (9 for thigh, 0 for lower leg)
        if (keyboardState[SDL_SCANCODE_9] && rightThighX > -90.0f && rightThighX < 45.0f) {
            rightThighX += rotationSpeed;  // Right thigh forward/back
        }
        if (keyboardState[SDL_SCANCODE_0] && rightLowerLegX > -120.0f) {
            rightLowerLegX -= rotationSpeed;  // Right lower leg bend (limited)
        }

        // Keep torso rotation in reasonable range (can spin freely)
        if (torsoRotationY > 360.0f) torsoRotationY -= 360.0f;
        if (torsoRotationY < -360.0f) torsoRotationY += 360.0f;

        // Clamp head rotations to realistic limits (safety check)
        if (headRotationX > 30.0f) headRotationX = 30.0f;
        if (headRotationX < -45.0f) headRotationX = -45.0f;
        if (headRotationY > 75.0f) headRotationY = 75.0f;
        if (headRotationY < -75.0f) headRotationY = -75.0f;

        // Clamp limb rotations to realistic limits
        if (leftForearmX < -120.0f) leftForearmX = -120.0f;
        if (rightForearmX < -120.0f) rightForearmX = -120.0f;
        if (leftLowerLegX < -120.0f) leftLowerLegX = -120.0f;
        if (rightLowerLegX < -120.0f) rightLowerLegX = -120.0f;
    }

public:
    Application() : window(nullptr), glContext(nullptr), running(false),
                   windowWidth(800), windowHeight(600),
                   torsoRotationY(0.0f), headRotationX(0.0f), headRotationY(0.0f),
                   leftUpperArmX(0.0f), leftUpperArmZ(0.0f), leftForearmX(0.0f),
                   rightUpperArmX(0.0f), rightUpperArmZ(0.0f), rightForearmX(0.0f),
                   leftThighX(0.0f), leftLowerLegX(0.0f),
                   rightThighX(0.0f), rightLowerLegX(0.0f),
                   keyboardState(nullptr) {}

    ~Application() {
        cleanup();
    }

    bool initialize() {
        // Initialize SDL
        if (SDL_Init(SDL_INIT_VIDEO) < 0) {
            std::cerr << "Failed to initialize SDL: " << SDL_GetError() << std::endl;
            return false;
        }

        // Set OpenGL attributes (using compatibility profile for immediate mode)
        SDL_GL_SetAttribute(SDL_GL_CONTEXT_MAJOR_VERSION, 2);
        SDL_GL_SetAttribute(SDL_GL_CONTEXT_MINOR_VERSION, 1);
        SDL_GL_SetAttribute(SDL_GL_DOUBLEBUFFER, 1);
        SDL_GL_SetAttribute(SDL_GL_DEPTH_SIZE, 24);

        // Create window
        window = SDL_CreateWindow(
            "HumanGL - Full Skeletal Animation",
            SDL_WINDOWPOS_CENTERED,
            SDL_WINDOWPOS_CENTERED,
            windowWidth,
            windowHeight,
            SDL_WINDOW_OPENGL | SDL_WINDOW_SHOWN
        );

        if (!window) {
            std::cerr << "Failed to create window: " << SDL_GetError() << std::endl;
            return false;
        }

        // Create OpenGL context
        glContext = SDL_GL_CreateContext(window);
        if (!glContext) {
            std::cerr << "Failed to create OpenGL context: " << SDL_GetError() << std::endl;
            return false;
        }

        // Enable VSync
        SDL_GL_SetSwapInterval(1);

        // Initialize OpenGL settings
        glViewport(0, 0, windowWidth, windowHeight);
        glClearColor(0.2f, 0.3f, 0.4f, 1.0f);  // Nice blue-gray background
        glEnable(GL_DEPTH_TEST);

        std::cout << "OpenGL Version: " << glGetString(GL_VERSION) << std::endl;
        std::cout << "OpenGL Renderer: " << glGetString(GL_RENDERER) << std::endl;

        // Set up 3D perspective projection
        setupPerspective();

        // Get keyboard state
        keyboardState = SDL_GetKeyboardState(nullptr);

        running = true;
        return true;
    }

    void handleEvents() {
        SDL_Event event;
        while (SDL_PollEvent(&event)) {
            switch (event.type) {
                case SDL_QUIT:
                    running = false;
                    break;
                case SDL_KEYDOWN:
                    if (event.key.keysym.sym == SDLK_ESCAPE) {
                        running = false;
                    }
                    break;
                case SDL_WINDOWEVENT:
                    if (event.window.event == SDL_WINDOWEVENT_RESIZED) {
                        windowWidth = event.window.data1;
                        windowHeight = event.window.data2;
                        glViewport(0, 0, windowWidth, windowHeight);
                        setupPerspective();  // Update perspective on resize
                    }
                    break;
            }
        }
    }

    void render() {
        // Clear the screen
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

        // Reset matrix stack
        matrixStack.loadIdentity();

        // Apply torso rotation (this affects all body parts)
        matrixStack.rotateY(torsoRotationY);

        // Draw body parts in hierarchical order
        drawTorso();           // Base of hierarchy
        drawNeck();            // Connected to torso
        drawHead();            // Connected to neck

        // Draw arms (connected to torso)
        drawLeftUpperArm();    // Left upper arm
        drawLeftForearm();     // Left forearm (follows upper arm)
        drawRightUpperArm();   // Right upper arm
        drawRightForearm();    // Right forearm (follows upper arm)

        // Draw legs (connected to torso)
        drawLeftThigh();       // Left thigh
        drawLeftLowerLeg();    // Left lower leg (follows thigh)
        drawRightThigh();      // Right thigh
        drawRightLowerLeg();   // Right lower leg (follows thigh)

        // Swap buffers
        SDL_GL_SwapWindow(window);
    }

    void run() {
        if (!initialize()) {
            return;
        }

        std::cout << "Skeletal Animation Demo:" << std::endl;
        std::cout << "A/D: Rotate torso (head follows naturally)" << std::endl;
        std::cout << "W/S: Head up/down (limited: 45° up, 30° down)" << std::endl;
        std::cout << "Q/E: Head left/right (limited: 75° each way)" << std::endl;
        std::cout << "ESC: Exit" << std::endl;

        while (running) {
            handleEvents();
            handleKeyboardInput();  // Handle continuous keyboard input
            render();

            // Small delay to prevent excessive CPU usage
            SDL_Delay(16); // ~60 FPS
        }
    }

    void cleanup() {
        if (glContext) {
            SDL_GL_DeleteContext(glContext);
            glContext = nullptr;
        }
        if (window) {
            SDL_DestroyWindow(window);
            window = nullptr;
        }
        SDL_Quit();
    }
};

int main(int argc, char* argv[]) {
    (void)argc;  // Suppress unused parameter warning
    (void)argv;  // Suppress unused parameter warning
    
    Application app;
    app.run();
    
    return 0;
}
